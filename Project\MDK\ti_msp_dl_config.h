/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for PWM_MOTOR_A */
#define PWM_MOTOR_A_INST                                                   TIMG0
#define PWM_MOTOR_A_INST_IRQHandler                             TIMG0_IRQHandler
#define PWM_MOTOR_A_INST_INT_IRQN                               (TIMG0_INT_IRQn)
#define PWM_MOTOR_A_INST_CLK_FREQ                                        5000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTOR_A_C0_PORT                                           GPIOA
#define GPIO_PWM_MOTOR_A_C0_PIN                                   DL_GPIO_PIN_12
#define GPIO_PWM_MOTOR_A_C0_IOMUX                                (IOMUX_PINCM34)
#define GPIO_PWM_MOTOR_A_C0_IOMUX_FUNC               IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_PWM_MOTOR_A_C0_IDX                              DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTOR_A_C1_PORT                                           GPIOA
#define GPIO_PWM_MOTOR_A_C1_PIN                                   DL_GPIO_PIN_13
#define GPIO_PWM_MOTOR_A_C1_IOMUX                                (IOMUX_PINCM35)
#define GPIO_PWM_MOTOR_A_C1_IOMUX_FUNC               IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_PWM_MOTOR_A_C1_IDX                              DL_TIMER_CC_1_INDEX



/* Defines for TIMER_FUNS */
#define TIMER_FUNS_INST                                                  (TIMG8)
#define TIMER_FUNS_INST_IRQHandler                              TIMG8_IRQHandler
#define TIMER_FUNS_INST_INT_IRQN                                (TIMG8_INT_IRQn)
#define TIMER_FUNS_INST_LOAD_VALUE                                        (199U)



/* Defines for UART_DEBUG */
#define UART_DEBUG_INST                                                    UART0
#define UART_DEBUG_INST_FREQUENCY                                       40000000
#define UART_DEBUG_INST_IRQHandler                              UART0_IRQHandler
#define UART_DEBUG_INST_INT_IRQN                                  UART0_INT_IRQn
#define GPIO_UART_DEBUG_RX_PORT                                            GPIOA
#define GPIO_UART_DEBUG_TX_PORT                                            GPIOA
#define GPIO_UART_DEBUG_RX_PIN                                    DL_GPIO_PIN_11
#define GPIO_UART_DEBUG_TX_PIN                                    DL_GPIO_PIN_10
#define GPIO_UART_DEBUG_IOMUX_RX                                 (IOMUX_PINCM22)
#define GPIO_UART_DEBUG_IOMUX_TX                                 (IOMUX_PINCM21)
#define GPIO_UART_DEBUG_IOMUX_RX_FUNC                  IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_DEBUG_IOMUX_TX_FUNC                  IOMUX_PINCM21_PF_UART0_TX
#define UART_DEBUG_BAUD_RATE                                           (1000000)
#define UART_DEBUG_IBRD_40_MHZ_1000000_BAUD                                  (2)
#define UART_DEBUG_FBRD_40_MHZ_1000000_BAUD                                 (32)
/* Defines for UART_JYXX */
#define UART_JYXX_INST                                                     UART3
#define UART_JYXX_INST_FREQUENCY                                        80000000
#define UART_JYXX_INST_IRQHandler                               UART3_IRQHandler
#define UART_JYXX_INST_INT_IRQN                                   UART3_INT_IRQn
#define GPIO_UART_JYXX_RX_PORT                                             GPIOA
#define GPIO_UART_JYXX_TX_PORT                                             GPIOA
#define GPIO_UART_JYXX_RX_PIN                                     DL_GPIO_PIN_25
#define GPIO_UART_JYXX_TX_PIN                                     DL_GPIO_PIN_26
#define GPIO_UART_JYXX_IOMUX_RX                                  (IOMUX_PINCM55)
#define GPIO_UART_JYXX_IOMUX_TX                                  (IOMUX_PINCM59)
#define GPIO_UART_JYXX_IOMUX_RX_FUNC                   IOMUX_PINCM55_PF_UART3_RX
#define GPIO_UART_JYXX_IOMUX_TX_FUNC                   IOMUX_PINCM59_PF_UART3_TX
#define UART_JYXX_BAUD_RATE                                               (9600)
#define UART_JYXX_IBRD_80_MHZ_9600_BAUD                                    (520)
#define UART_JYXX_FBRD_80_MHZ_9600_BAUD                                     (53)





/* Defines for ADC */
#define ADC_INST                                                            ADC1
#define ADC_INST_IRQHandler                                      ADC1_IRQHandler
#define ADC_INST_INT_IRQN                                        (ADC1_INT_IRQn)
#define ADC_ADCMEM_ADC_KEY                                    DL_ADC12_MEM_IDX_0
#define ADC_ADCMEM_ADC_KEY_REF                   DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC_ADCMEM_ADC_KEY_REF_VOLTAGE_V                                     3.3
#define GPIO_ADC_C0_PORT                                                   GPIOA
#define GPIO_ADC_C0_PIN                                           DL_GPIO_PIN_15



/* Defines for DMA_CH0 */
#define DMA_CH0_CHAN_ID                                                      (0)
#define ADC_INST_DMA_TRIGGER                          (DMA_ADC1_EVT_GEN_BD_TRIG)



/* Defines for LED1: GPIOB.14 with pinCMx 31 on package pin 2 */
#define LED_LED1_PORT                                                    (GPIOB)
#define LED_LED1_PIN                                            (DL_GPIO_PIN_14)
#define LED_LED1_IOMUX                                           (IOMUX_PINCM31)
/* Defines for LED2: GPIOA.18 with pinCMx 40 on package pin 11 */
#define LED_LED2_PORT                                                    (GPIOA)
#define LED_LED2_PIN                                            (DL_GPIO_PIN_18)
#define LED_LED2_IOMUX                                           (IOMUX_PINCM40)
/* Defines for LED3: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_LED3_PORT                                                    (GPIOB)
#define LED_LED3_PIN                                            (DL_GPIO_PIN_22)
#define LED_LED3_IOMUX                                           (IOMUX_PINCM50)
/* Defines for LED4: GPIOA.7 with pinCMx 14 on package pin 49 */
#define LED_LED4_PORT                                                    (GPIOA)
#define LED_LED4_PIN                                             (DL_GPIO_PIN_7)
#define LED_LED4_IOMUX                                           (IOMUX_PINCM14)
/* Port definition for Pin Group SOFT_I2C0 */
#define SOFT_I2C0_PORT                                                   (GPIOB)

/* Defines for SDA: GPIOB.0 with pinCMx 12 on package pin 47 */
#define SOFT_I2C0_SDA_PIN                                        (DL_GPIO_PIN_0)
#define SOFT_I2C0_SDA_IOMUX                                      (IOMUX_PINCM12)
/* Defines for SCL: GPIOB.1 with pinCMx 13 on package pin 48 */
#define SOFT_I2C0_SCL_PIN                                        (DL_GPIO_PIN_1)
#define SOFT_I2C0_SCL_IOMUX                                      (IOMUX_PINCM13)
/* Port definition for Pin Group TB6612 */
#define TB6612_PORT                                                      (GPIOB)

/* Defines for AIN1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define TB6612_AIN1_PIN                                          (DL_GPIO_PIN_9)
#define TB6612_AIN1_IOMUX                                        (IOMUX_PINCM26)
/* Defines for AIN2: GPIOB.8 with pinCMx 25 on package pin 60 */
#define TB6612_AIN2_PIN                                          (DL_GPIO_PIN_8)
#define TB6612_AIN2_IOMUX                                        (IOMUX_PINCM25)
/* Defines for BIN1: GPIOB.7 with pinCMx 24 on package pin 59 */
#define TB6612_BIN1_PIN                                          (DL_GPIO_PIN_7)
#define TB6612_BIN1_IOMUX                                        (IOMUX_PINCM24)
/* Defines for BIN2: GPIOB.6 with pinCMx 23 on package pin 58 */
#define TB6612_BIN2_PIN                                          (DL_GPIO_PIN_6)
#define TB6612_BIN2_IOMUX                                        (IOMUX_PINCM23)
/* Port definition for Pin Group ENCODER */
#define ENCODER_PORT                                                     (GPIOB)

/* Defines for A1: GPIOB.15 with pinCMx 32 on package pin 3 */
// pins affected by this interrupt request:["A1","A2"]
#define ENCODER_INT_IRQN                                        (GPIOB_INT_IRQn)
#define ENCODER_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define ENCODER_A1_IIDX                                     (DL_GPIO_IIDX_DIO15)
#define ENCODER_A1_PIN                                          (DL_GPIO_PIN_15)
#define ENCODER_A1_IOMUX                                         (IOMUX_PINCM32)
/* Defines for B1: GPIOB.16 with pinCMx 33 on package pin 4 */
#define ENCODER_B1_PIN                                          (DL_GPIO_PIN_16)
#define ENCODER_B1_IOMUX                                         (IOMUX_PINCM33)
/* Defines for A2: GPIOB.11 with pinCMx 28 on package pin 63 */
#define ENCODER_A2_IIDX                                     (DL_GPIO_IIDX_DIO11)
#define ENCODER_A2_PIN                                          (DL_GPIO_PIN_11)
#define ENCODER_A2_IOMUX                                         (IOMUX_PINCM28)
/* Defines for B2: GPIOB.10 with pinCMx 27 on package pin 62 */
#define ENCODER_B2_PIN                                          (DL_GPIO_PIN_10)
#define ENCODER_B2_IOMUX                                         (IOMUX_PINCM27)
/* Defines for A: GPIOA.8 with pinCMx 19 on package pin 54 */
#define TRACK_A_PORT                                                     (GPIOA)
#define TRACK_A_PIN                                              (DL_GPIO_PIN_8)
#define TRACK_A_IOMUX                                            (IOMUX_PINCM19)
/* Defines for B: GPIOA.9 with pinCMx 20 on package pin 55 */
#define TRACK_B_PORT                                                     (GPIOA)
#define TRACK_B_PIN                                              (DL_GPIO_PIN_9)
#define TRACK_B_IOMUX                                            (IOMUX_PINCM20)
/* Defines for C: GPIOB.12 with pinCMx 29 on package pin 64 */
#define TRACK_C_PORT                                                     (GPIOB)
#define TRACK_C_PIN                                             (DL_GPIO_PIN_12)
#define TRACK_C_IOMUX                                            (IOMUX_PINCM29)
/* Defines for D: GPIOB.26 with pinCMx 57 on package pin 28 */
#define TRACK_D_PORT                                                     (GPIOB)
#define TRACK_D_PIN                                             (DL_GPIO_PIN_26)
#define TRACK_D_IOMUX                                            (IOMUX_PINCM57)
/* Defines for E: GPIOB.4 with pinCMx 17 on package pin 52 */
#define TRACK_E_PORT                                                     (GPIOB)
#define TRACK_E_PIN                                              (DL_GPIO_PIN_4)
#define TRACK_E_IOMUX                                            (IOMUX_PINCM17)
/* Defines for F: GPIOB.5 with pinCMx 18 on package pin 53 */
#define TRACK_F_PORT                                                     (GPIOB)
#define TRACK_F_PIN                                              (DL_GPIO_PIN_5)
#define TRACK_F_IOMUX                                            (IOMUX_PINCM18)
/* Defines for G: GPIOB.13 with pinCMx 30 on package pin 1 */
#define TRACK_G_PORT                                                     (GPIOB)
#define TRACK_G_PIN                                             (DL_GPIO_PIN_13)
#define TRACK_G_IOMUX                                            (IOMUX_PINCM30)
/* Defines for H: GPIOB.27 with pinCMx 58 on package pin 29 */
#define TRACK_H_PORT                                                     (GPIOB)
#define TRACK_H_PIN                                             (DL_GPIO_PIN_27)
#define TRACK_H_IOMUX                                            (IOMUX_PINCM58)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_SYSCTL_CLK_init(void);
void SYSCFG_DL_PWM_MOTOR_A_init(void);
void SYSCFG_DL_TIMER_FUNS_init(void);
void SYSCFG_DL_UART_DEBUG_init(void);
void SYSCFG_DL_UART_JYXX_init(void);
void SYSCFG_DL_ADC_init(void);
void SYSCFG_DL_DMA_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
