T93BC 000:323.637   SEGGER J-Link V8.54 Log File
T93BC 000:323.901   DLL Compiled: Jul 23 2025 12:15:33
T93BC 000:323.927   Logging started @ 2025-08-01 07:14
T93BC 000:323.951   Process: D:\HuanJ\Keil5\UV4\UV4.exe
T93BC 000:323.986 - 323.974ms 
T93BC 000:324.021 JLINK_SetWarnOutHandler(...)
T93BC 000:324.045 - 0.025ms 
T93BC 000:324.069 JLINK_OpenEx(...)
T93BC 000:328.512   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T93BC 000:329.872   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T93BC 000:332.543   Hardware: V7.00
T93BC 000:332.573   S/N: 20090929
T93BC 000:332.593   OEM: SEGGER
T93BC 000:332.612   Feature(s): R<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,GDBFull
T93BC 000:333.148   Bootloader: (FW returned invalid version)
T93BC 000:334.437   TELNET listener socket opened on port 19021
T93BC 000:334.513   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T93BC 000:334.625   WEBSRV Webserver running on local port 19080
T93BC 000:334.755   Looking for J-Link GUI Server exe at: D:\HuanJ\Keil5\ARM\Segger\JLinkGUIServer.exe
T93BC 000:334.859   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink\JLinkGUIServer.exe
T93BC 000:334.921   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink\JLinkGUIServer.exe
T93BC 000:613.008   J-Link GUI Server info: "J-Link GUI server V8.54 "
T93BC 000:613.645 - 289.570ms returns "O.K."
T93BC 000:613.685 JLINK_GetEmuCaps()
T93BC 000:613.702 - 0.014ms returns 0x88EA5833
T93BC 000:613.720 JLINK_TIF_GetAvailable(...)
T93BC 000:614.158 - 0.438ms 
T93BC 000:614.188 JLINK_SetErrorOutHandler(...)
T93BC 000:614.202 - 0.014ms 
T93BC 000:614.244 JLINK_ExecCommand("ProjectFile = "D:\DanPJ\micu\DianS\M0\He2\ti-msp-master\Project\MDK\JLinkSettings.ini"", ...). 
T93BC 000:629.214   Ref file found at: D:\HuanJ\Keil5\ARM\Segger\JLinkDevices.ref
T93BC 000:629.364   REF file references invalid XML file: C:\Program Files\SEGGER\JLink\JLinkDevices.xml
T93BC 000:632.874   Device "CORTEX-M0" selected.
T93BC 000:633.243 - 19.001ms returns 0x00
T93BC 000:634.862 JLINK_ExecCommand("Device = MSPM0G3507", ...). 
T93BC 000:636.655   Device "MSPM0G3507" selected.
T93BC 000:636.981 - 2.086ms returns 0x00
T93BC 000:637.004 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T93BC 000:637.028   ERROR: Unknown command
T93BC 000:637.048 - 0.029ms returns 0x01
T93BC 000:637.063 JLINK_GetHardwareVersion()
T93BC 000:637.076 - 0.013ms returns 70000
T93BC 000:637.090 JLINK_GetDLLVersion()
T93BC 000:637.104 - 0.013ms returns 85400
T93BC 000:637.117 JLINK_GetOEMString(...)
T93BC 000:637.131 JLINK_GetFirmwareString(...)
T93BC 000:637.144 - 0.013ms 
T93BC 000:640.769 JLINK_GetDLLVersion()
T93BC 000:640.803 - 0.034ms returns 85400
T93BC 000:640.818 JLINK_GetCompileDateTime()
T93BC 000:640.831 - 0.013ms 
T93BC 000:641.998 JLINK_GetFirmwareString(...)
T93BC 000:642.025 - 0.026ms 
T93BC 000:643.086 JLINK_GetHardwareVersion()
T93BC 000:643.110 - 0.023ms returns 70000
T93BC 000:644.278 JLINK_GetSN()
T93BC 000:644.305 - 0.026ms returns 20090929
T93BC 000:645.550 JLINK_GetOEMString(...)
T93BC 000:648.911 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T93BC 000:650.114 - 1.208ms returns 0x00
T93BC 000:650.136 JLINK_HasError()
T93BC 000:650.159 JLINK_SetSpeed(5000)
T93BC 000:650.457 - 0.300ms 
T93BC 000:650.475 JLINK_GetId()
T93BC 000:652.978   InitTarget() start
T93BC 000:653.028    J-Link Script File: Executing InitTarget()
T93BC 000:657.397   DAP initialized successfully.
T93BC 000:661.543   Setting up LPM debug bits
T93BC 000:670.027   InitTarget() end - Took 12.0ms
T93BC 000:673.760   Found SW-DP with ID 0x6BA02477
T93BC 000:682.443   Old FW that does not support reading DPIDR via DAP jobs
T93BC 000:694.422   DPv0 detected
T93BC 000:696.958   CoreSight SoC-400 or earlier
T93BC 000:702.215   Scanning AP map to find all available APs
T93BC 000:721.693   AP[5]: Stopped AP scan as end of AP map has been reached
T93BC 000:723.955   AP[0]: AHB-AP (IDR: 0x84770001, ADDR: 0x00000000)
T93BC 000:725.577   AP[1]: MEM-AP (IDR: 0x002E0001, ADDR: 0x01000000)
T93BC 000:726.942   AP[2]: JTAG-AP (IDR: 0x002E0000, ADDR: 0x02000000)
T93BC 000:728.257   AP[3]: MEM-AP (IDR: 0x002E0003, ADDR: 0x03000000)
T93BC 000:729.344   AP[4]: MEM-AP (IDR: 0x002E0002, ADDR: 0x04000000)
T93BC 000:732.511   Iterating through AP map to find AHB-AP to use
T93BC 000:742.905   AP[0]: Core found
T93BC 000:744.212   AP[0]: AHB-AP ROM base: 0xF0000000
T93BC 000:751.453   CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)
T93BC 000:753.569   Found Cortex-M0 r0p1, Little endian.
T93BC 000:754.431   -- Max. mem block: 0x00002C18
T93BC 000:755.325   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 000:755.945   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T93BC 000:756.606   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:758.675   FPUnit: 4 code (BP) slots and 0 literal slots
T93BC 000:758.728   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T93BC 000:759.463   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 000:760.150   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:760.844   CPU_WriteMem(4 bytes @ 0x********)
T93BC 000:764.164   CoreSight components:
T93BC 000:765.818   ROMTbl[0] @ F0000000
T93BC 000:765.860   CPU_ReadMem(64 bytes @ 0xF0000000)
T93BC 000:767.104   CPU_ReadMem(32 bytes @ 0xE00FFFE0)
T93BC 000:769.594   [0][0]: E00FF000 CID B105100D PID 000BB4C0 ROM Table
T93BC 000:770.752   ROMTbl[1] @ E00FF000
T93BC 000:770.786   CPU_ReadMem(64 bytes @ 0xE00FF000)
T93BC 000:772.022   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T93BC 000:774.191   [1][0]: E000E000 CID B105E00D PID 000BB008 SCS
T93BC 000:774.231   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T93BC 000:776.434   [1][1]: ******** CID B105E00D PID 000BB00A DWT
T93BC 000:776.472   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T93BC 000:779.802   [1][2]: ******** CID B105E00D PID 000BB00B FPB
T93BC 000:779.838   CPU_ReadMem(32 bytes @ 0x40402FE0)
T93BC 000:782.463   [0][2]: 40402000 CID B105900D PID 001BB932 MTB-M0+
T93BC 000:783.389 - 132.913ms returns 0x6BA02477
T93BC 000:783.448 JLINK_GetDLLVersion()
T93BC 000:783.462 - 0.014ms returns 85400
T93BC 000:783.476 JLINK_CORE_GetFound()
T93BC 000:783.489 - 0.013ms returns 0x60000FF
T93BC 000:783.503 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T93BC 000:783.523   Value=0xF0000000
T93BC 000:783.543 - 0.040ms returns 0
T93BC 000:785.053 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T93BC 000:785.080   Value=0xF0000000
T93BC 000:785.100 - 0.048ms returns 0
T93BC 000:785.115 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T93BC 000:785.128   Value=0x00000000
T93BC 000:785.148 - 0.032ms returns 0
T93BC 000:785.162 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T93BC 000:785.204   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T93BC 000:786.062   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T93BC 000:786.096 - 0.933ms returns 16 (0x10)
T93BC 000:786.111 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T93BC 000:786.124   Value=0x40402000
T93BC 000:786.143 - 0.031ms returns 0
T93BC 000:786.156 JLINK_CORE_GetFound()
T93BC 000:786.169 - 0.012ms returns 0x60000FF
T93BC 000:786.182 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T93BC 000:786.195   Value=0x00000000
T93BC 000:786.213 - 0.031ms returns 0
T93BC 000:786.227 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T93BC 000:786.240   Value=0xE0000000
T93BC 000:786.259 - 0.032ms returns 0
T93BC 000:786.273 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T93BC 000:786.286   Value=0x********
T93BC 000:786.305 - 0.032ms returns 0
T93BC 000:786.319 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T93BC 000:786.332   Value=0x********
T93BC 000:786.351 - 0.031ms returns 0
T93BC 000:786.365 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T93BC 000:786.377   Value=0xE000E000
T93BC 000:786.396 - 0.031ms returns 0
T93BC 000:786.410 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T93BC 000:786.422   Value=0xE000EDF0
T93BC 000:786.441 - 0.031ms returns 0
T93BC 000:786.455 JLINK_GetDebugInfo(0x01 = Unknown)
T93BC 000:786.472   Value=0x00000000
T93BC 000:786.495 - 0.041ms returns 0
T93BC 000:786.509 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T93BC 000:786.530   CPU_ReadMem(4 bytes @ 0xE000ED00)
T93BC 000:787.189   Data:  01 C6 0C 41
T93BC 000:787.238   Debug reg: CPUID
T93BC 000:787.258 - 0.748ms returns 1 (0x1)
T93BC 000:787.272 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T93BC 000:787.286   Value=0x00000000
T93BC 000:787.305 - 0.033ms returns 0
T93BC 000:787.319 JLINK_HasError()
T93BC 000:787.334 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T93BC 000:787.348 - 0.013ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T93BC 000:787.361 JLINK_Reset()
T93BC 000:788.614   ResetTarget() start
T93BC 000:788.648    J-Link Script File: Executing ResetTarget()
T93BC 000:788.668   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 000:789.485   CPU is running
T93BC 000:789.517   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T93BC 000:790.220   CPU_ReadMem(4 bytes @ 0x400B0300)
T93BC 000:790.907   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T93BC 000:791.645   CPU is running
T93BC 000:791.668   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 000:792.398   CPU is running
T93BC 000:792.447   CPU_WriteMem(4 bytes @ 0x400B0300)
T93BC 000:793.081   CPU is running
T93BC 000:793.106   CPU_WriteMem(4 bytes @ 0x400B0304)
T93BC 000:793.836   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 000:800.840   DAP initialized successfully.
T93BC 000:800.908   CPU is running
T93BC 000:800.942   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 000:803.408   ResetTarget() end - Took 13.0ms
T93BC 000:804.731   Device specific reset executed.
T93BC 000:808.157   CPU_WriteMem(4 bytes @ 0x********)
T93BC 000:808.866   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T93BC 000:809.528   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:810.228   CPU_WriteMem(4 bytes @ 0x********)
T93BC 000:811.000 - 23.638ms 
T93BC 000:811.055 JLINK_Halt()
T93BC 000:811.075 - 0.020ms returns 0x00
T93BC 000:811.096 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T93BC 000:811.121   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 000:811.942   Data:  03 00 03 01
T93BC 000:811.972   Debug reg: DHCSR
T93BC 000:811.999 - 0.903ms returns 1 (0x1)
T93BC 000:812.020 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T93BC 000:812.039   Debug reg: DHCSR
T93BC 000:812.369   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T93BC 000:813.125 - 1.105ms returns 0 (0x00000000)
T93BC 000:813.148 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T93BC 000:813.168   Debug reg: DEMCR
T93BC 000:813.199   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 000:813.917 - 0.769ms returns 0 (0x00000000)
T93BC 000:819.671 JLINK_GetHWStatus(...)
T93BC 000:820.168 - 0.496ms returns 0
T93BC 000:824.168 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T93BC 000:824.203 - 0.035ms returns 0x04
T93BC 000:824.218 JLINK_GetNumBPUnits(Type = 0xF0)
T93BC 000:824.231 - 0.013ms returns 0x2000
T93BC 000:824.245 JLINK_GetNumWPUnits()
T93BC 000:824.258 - 0.012ms returns 2
T93BC 000:827.294 JLINK_GetSpeed()
T93BC 000:827.323 - 0.028ms returns 4000
T93BC 000:830.890 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T93BC 000:830.928   CPU_ReadMem(4 bytes @ 0xE000E004)
T93BC 000:831.711   Data:  00 00 00 00
T93BC 000:831.747 - 0.857ms returns 1 (0x1)
T93BC 000:831.767 JLINK_Halt()
T93BC 000:831.780 - 0.013ms returns 0x00
T93BC 000:831.794 JLINK_IsHalted()
T93BC 000:831.820 - 0.025ms returns TRUE
T93BC 000:843.164 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T93BC 000:843.197   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T93BC 000:843.526   CPU_WriteMem(660 bytes @ 0x20200000)
T93BC 000:851.051 - 7.886ms returns 0x294
T93BC 000:851.151 JLINK_HasError()
T93BC 000:851.198 JLINK_WriteReg(R0, 0x00000000)
T93BC 000:851.227 - 0.053ms returns 0
T93BC 000:851.247 JLINK_WriteReg(R1, 0x01F78A40)
T93BC 000:851.266 - 0.018ms returns 0
T93BC 000:851.285 JLINK_WriteReg(R2, 0x00000001)
T93BC 000:851.303 - 0.018ms returns 0
T93BC 000:851.323 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:851.342 - 0.019ms returns 0
T93BC 000:851.362 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:851.380 - 0.018ms returns 0
T93BC 000:851.403 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:851.426 - 0.023ms returns 0
T93BC 000:851.446 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:851.465 - 0.019ms returns 0
T93BC 000:851.484 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:851.503 - 0.018ms returns 0
T93BC 000:851.530 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:851.549 - 0.026ms returns 0
T93BC 000:851.569 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:851.588 - 0.018ms returns 0
T93BC 000:851.607 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:851.627 - 0.019ms returns 0
T93BC 000:851.651 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:851.670 - 0.018ms returns 0
T93BC 000:851.689 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:851.708 - 0.018ms returns 0
T93BC 000:851.728 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:851.748 - 0.020ms returns 0
T93BC 000:851.768 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:851.786 - 0.018ms returns 0
T93BC 000:851.810 JLINK_WriteReg(R15 (PC), 0x20200038)
T93BC 000:851.829 - 0.022ms returns 0
T93BC 000:851.849 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:851.868 - 0.019ms returns 0
T93BC 000:851.887 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:851.906 - 0.019ms returns 0
T93BC 000:851.926 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:851.945 - 0.018ms returns 0
T93BC 000:851.965 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:851.984 - 0.019ms returns 0
T93BC 000:852.004 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:852.030   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:852.739 - 0.734ms returns 0x00000001
T93BC 000:852.762 JLINK_Go()
T93BC 000:852.790   CPU_WriteMem(2 bytes @ 0x20200000)
T93BC 000:853.539   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:854.236   CPU_WriteMem(4 bytes @ 0x********)
T93BC 000:854.990   CPU_WriteMem(4 bytes @ 0xE0002008)
T93BC 000:855.023   CPU_WriteMem(4 bytes @ 0xE000200C)
T93BC 000:855.051   CPU_WriteMem(4 bytes @ 0xE0002010)
T93BC 000:855.078   CPU_WriteMem(4 bytes @ 0xE0002014)
T93BC 000:856.751   CPU_WriteMem(4 bytes @ 0xE0001004)
T93BC 000:863.375   Memory map 'after startup completion point' is active
T93BC 000:863.443 - 10.681ms 
T93BC 000:863.483 JLINK_IsHalted()
T93BC 000:866.949   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:867.642 - 4.158ms returns TRUE
T93BC 000:867.679 JLINK_ReadReg(R15 (PC))
T93BC 000:867.707 - 0.027ms returns 0x20200000
T93BC 000:867.734 JLINK_ClrBPEx(BPHandle = 0x00000001)
T93BC 000:867.759 - 0.024ms returns 0x00
T93BC 000:867.786 JLINK_ReadReg(R0)
T93BC 000:867.811 - 0.024ms returns 0x00000000
T93BC 000:868.764 JLINK_HasError()
T93BC 000:868.810 JLINK_WriteReg(R0, 0x00000000)
T93BC 000:868.838 - 0.027ms returns 0
T93BC 000:868.863 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:868.886 - 0.023ms returns 0
T93BC 000:868.910 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:868.934 - 0.023ms returns 0
T93BC 000:868.958 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:868.981 - 0.023ms returns 0
T93BC 000:869.006 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:869.029 - 0.023ms returns 0
T93BC 000:869.054 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:869.077 - 0.023ms returns 0
T93BC 000:869.101 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:869.125 - 0.023ms returns 0
T93BC 000:869.149 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:869.172 - 0.023ms returns 0
T93BC 000:869.197 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:869.220 - 0.022ms returns 0
T93BC 000:869.243 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:869.265 - 0.022ms returns 0
T93BC 000:869.288 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:869.310 - 0.022ms returns 0
T93BC 000:869.334 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:869.357 - 0.023ms returns 0
T93BC 000:869.380 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:869.403 - 0.023ms returns 0
T93BC 000:869.426 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:869.450 - 0.024ms returns 0
T93BC 000:869.508 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:869.545 - 0.036ms returns 0
T93BC 000:869.571 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 000:869.596 - 0.025ms returns 0
T93BC 000:869.621 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:869.644 - 0.023ms returns 0
T93BC 000:869.722 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:869.749 - 0.026ms returns 0
T93BC 000:869.772 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:869.795 - 0.022ms returns 0
T93BC 000:869.818 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:869.841 - 0.022ms returns 0
T93BC 000:869.865 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:869.890 - 0.025ms returns 0x00000002
T93BC 000:869.913 JLINK_Go()
T93BC 000:869.946   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:874.112 - 4.197ms 
T93BC 000:874.154 JLINK_IsHalted()
T93BC 000:877.527   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:878.260 - 4.105ms returns TRUE
T93BC 000:878.302 JLINK_ReadReg(R15 (PC))
T93BC 000:878.339 - 0.037ms returns 0x20200000
T93BC 000:878.375 JLINK_ClrBPEx(BPHandle = 0x00000002)
T93BC 000:878.403 - 0.027ms returns 0x00
T93BC 000:878.421 JLINK_ReadReg(R0)
T93BC 000:878.442 - 0.021ms returns 0x00000001
T93BC 000:878.460 JLINK_HasError()
T93BC 000:878.478 JLINK_WriteReg(R0, 0x00000000)
T93BC 000:878.495 - 0.017ms returns 0
T93BC 000:878.513 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:878.530 - 0.017ms returns 0
T93BC 000:878.547 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:878.563 - 0.016ms returns 0
T93BC 000:878.581 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:878.598 - 0.016ms returns 0
T93BC 000:878.615 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:878.632 - 0.016ms returns 0
T93BC 000:878.649 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:878.666 - 0.016ms returns 0
T93BC 000:878.683 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:878.700 - 0.016ms returns 0
T93BC 000:878.717 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:878.734 - 0.016ms returns 0
T93BC 000:878.751 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:878.768 - 0.016ms returns 0
T93BC 000:878.786 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:878.803 - 0.016ms returns 0
T93BC 000:878.820 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:878.838 - 0.018ms returns 0
T93BC 000:878.856 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:878.873 - 0.016ms returns 0
T93BC 000:878.890 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:878.907 - 0.016ms returns 0
T93BC 000:878.924 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:878.941 - 0.017ms returns 0
T93BC 000:878.959 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:878.976 - 0.016ms returns 0
T93BC 000:878.993 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 000:879.010 - 0.016ms returns 0
T93BC 000:879.028 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:879.044 - 0.016ms returns 0
T93BC 000:879.062 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:879.079 - 0.017ms returns 0
T93BC 000:879.097 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:879.113 - 0.016ms returns 0
T93BC 000:879.131 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:879.147 - 0.016ms returns 0
T93BC 000:879.165 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:879.183 - 0.017ms returns 0x00000003
T93BC 000:879.200 JLINK_Go()
T93BC 000:879.222   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:883.192 - 3.990ms 
T93BC 000:883.250 JLINK_IsHalted()
T93BC 000:884.052 - 0.800ms returns FALSE
T93BC 000:884.132 JLINK_HasError()
T93BC 000:902.554 JLINK_IsHalted()
T93BC 000:906.142   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:906.946 - 4.391ms returns TRUE
T93BC 000:906.995 JLINK_ReadReg(R15 (PC))
T93BC 000:907.037 - 0.042ms returns 0x20200000
T93BC 000:907.076 JLINK_ClrBPEx(BPHandle = 0x00000003)
T93BC 000:907.113 - 0.037ms returns 0x00
T93BC 000:907.156 JLINK_ReadReg(R0)
T93BC 000:907.192 - 0.035ms returns 0x00000000
T93BC 000:908.348 JLINK_HasError()
T93BC 000:908.382 JLINK_WriteReg(R0, 0x00000400)
T93BC 000:908.399 - 0.016ms returns 0
T93BC 000:908.413 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:908.427 - 0.013ms returns 0
T93BC 000:908.441 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:908.455 - 0.014ms returns 0
T93BC 000:908.468 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:908.481 - 0.013ms returns 0
T93BC 000:908.495 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:908.508 - 0.013ms returns 0
T93BC 000:908.521 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:908.534 - 0.013ms returns 0
T93BC 000:908.547 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:908.565 - 0.017ms returns 0
T93BC 000:908.578 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:908.591 - 0.012ms returns 0
T93BC 000:908.604 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:908.617 - 0.013ms returns 0
T93BC 000:908.631 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:908.644 - 0.012ms returns 0
T93BC 000:908.657 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:908.670 - 0.013ms returns 0
T93BC 000:908.684 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:908.696 - 0.012ms returns 0
T93BC 000:908.710 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:908.723 - 0.012ms returns 0
T93BC 000:908.736 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:908.749 - 0.013ms returns 0
T93BC 000:908.763 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:908.776 - 0.012ms returns 0
T93BC 000:908.789 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 000:908.802 - 0.013ms returns 0
T93BC 000:908.816 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:908.829 - 0.013ms returns 0
T93BC 000:908.842 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:908.855 - 0.012ms returns 0
T93BC 000:908.868 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:908.881 - 0.012ms returns 0
T93BC 000:908.894 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:908.907 - 0.013ms returns 0
T93BC 000:908.921 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:908.936 - 0.015ms returns 0x00000004
T93BC 000:908.950 JLINK_Go()
T93BC 000:908.973   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:912.965 - 4.014ms 
T93BC 000:912.982 JLINK_IsHalted()
T93BC 000:916.549   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:917.242 - 4.259ms returns TRUE
T93BC 000:917.287 JLINK_ReadReg(R15 (PC))
T93BC 000:917.307 - 0.019ms returns 0x20200000
T93BC 000:917.330 JLINK_ClrBPEx(BPHandle = 0x00000004)
T93BC 000:917.351 - 0.021ms returns 0x00
T93BC 000:917.369 JLINK_ReadReg(R0)
T93BC 000:917.386 - 0.016ms returns 0x00000001
T93BC 000:917.403 JLINK_HasError()
T93BC 000:917.421 JLINK_WriteReg(R0, 0x00000400)
T93BC 000:917.438 - 0.017ms returns 0
T93BC 000:917.456 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:917.472 - 0.016ms returns 0
T93BC 000:917.489 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:917.506 - 0.016ms returns 0
T93BC 000:917.524 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:917.540 - 0.016ms returns 0
T93BC 000:917.558 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:917.575 - 0.016ms returns 0
T93BC 000:917.592 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:917.609 - 0.017ms returns 0
T93BC 000:917.626 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:917.643 - 0.017ms returns 0
T93BC 000:917.661 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:917.678 - 0.016ms returns 0
T93BC 000:917.695 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:917.712 - 0.016ms returns 0
T93BC 000:917.729 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:917.746 - 0.016ms returns 0
T93BC 000:917.770 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:917.786 - 0.016ms returns 0
T93BC 000:917.804 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:917.820 - 0.016ms returns 0
T93BC 000:917.838 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:917.854 - 0.016ms returns 0
T93BC 000:917.872 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:917.889 - 0.017ms returns 0
T93BC 000:917.906 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:917.923 - 0.016ms returns 0
T93BC 000:917.940 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 000:917.957 - 0.016ms returns 0
T93BC 000:917.974 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:917.991 - 0.016ms returns 0
T93BC 000:918.008 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:918.024 - 0.016ms returns 0
T93BC 000:918.042 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:918.059 - 0.016ms returns 0
T93BC 000:918.076 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:918.093 - 0.016ms returns 0
T93BC 000:918.111 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:918.128 - 0.017ms returns 0x00000005
T93BC 000:918.146 JLINK_Go()
T93BC 000:918.171   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:922.044 - 3.897ms 
T93BC 000:922.088 JLINK_IsHalted()
T93BC 000:922.725 - 0.634ms returns FALSE
T93BC 000:922.756 JLINK_HasError()
T93BC 000:923.960 JLINK_IsHalted()
T93BC 000:927.465   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:928.124 - 4.162ms returns TRUE
T93BC 000:928.167 JLINK_ReadReg(R15 (PC))
T93BC 000:928.201 - 0.033ms returns 0x20200000
T93BC 000:928.233 JLINK_ClrBPEx(BPHandle = 0x00000005)
T93BC 000:928.264 - 0.030ms returns 0x00
T93BC 000:928.295 JLINK_ReadReg(R0)
T93BC 000:928.325 - 0.030ms returns 0x00000000
T93BC 000:929.111 JLINK_HasError()
T93BC 000:929.163 JLINK_WriteReg(R0, 0x00000800)
T93BC 000:929.197 - 0.033ms returns 0
T93BC 000:929.229 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:929.259 - 0.030ms returns 0
T93BC 000:929.290 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:929.320 - 0.029ms returns 0
T93BC 000:929.351 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:929.380 - 0.029ms returns 0
T93BC 000:929.412 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:929.441 - 0.029ms returns 0
T93BC 000:929.472 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:929.502 - 0.029ms returns 0
T93BC 000:929.533 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:929.563 - 0.029ms returns 0
T93BC 000:929.593 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:929.623 - 0.029ms returns 0
T93BC 000:929.654 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:929.684 - 0.030ms returns 0
T93BC 000:929.715 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:929.744 - 0.029ms returns 0
T93BC 000:929.776 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:929.806 - 0.030ms returns 0
T93BC 000:929.838 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:929.892 - 0.053ms returns 0
T93BC 000:929.922 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:929.951 - 0.029ms returns 0
T93BC 000:929.982 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:930.012 - 0.030ms returns 0
T93BC 000:930.042 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:930.071 - 0.029ms returns 0
T93BC 000:930.102 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 000:930.131 - 0.029ms returns 0
T93BC 000:930.161 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:930.190 - 0.029ms returns 0
T93BC 000:930.220 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:930.249 - 0.029ms returns 0
T93BC 000:930.278 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:930.306 - 0.027ms returns 0
T93BC 000:930.335 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:930.362 - 0.027ms returns 0
T93BC 000:930.392 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:930.421 - 0.029ms returns 0x00000006
T93BC 000:930.450 JLINK_Go()
T93BC 000:930.487   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:934.873 - 4.422ms 
T93BC 000:934.920 JLINK_IsHalted()
T93BC 000:938.415   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:939.116 - 4.194ms returns TRUE
T93BC 000:939.154 JLINK_ReadReg(R15 (PC))
T93BC 000:939.205 - 0.050ms returns 0x20200000
T93BC 000:939.244 JLINK_ClrBPEx(BPHandle = 0x00000006)
T93BC 000:939.275 - 0.031ms returns 0x00
T93BC 000:939.308 JLINK_ReadReg(R0)
T93BC 000:939.339 - 0.030ms returns 0x00000001
T93BC 000:939.371 JLINK_HasError()
T93BC 000:939.403 JLINK_WriteReg(R0, 0x00000800)
T93BC 000:939.435 - 0.031ms returns 0
T93BC 000:939.466 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:939.496 - 0.029ms returns 0
T93BC 000:939.527 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:939.558 - 0.030ms returns 0
T93BC 000:939.590 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:939.620 - 0.030ms returns 0
T93BC 000:939.653 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:939.683 - 0.030ms returns 0
T93BC 000:939.715 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:939.746 - 0.031ms returns 0
T93BC 000:939.778 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:939.808 - 0.030ms returns 0
T93BC 000:939.840 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:939.870 - 0.030ms returns 0
T93BC 000:939.915 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:939.946 - 0.031ms returns 0
T93BC 000:939.979 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:940.011 - 0.031ms returns 0
T93BC 000:940.044 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:940.075 - 0.031ms returns 0
T93BC 000:940.108 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:940.140 - 0.031ms returns 0
T93BC 000:940.174 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:940.205 - 0.031ms returns 0
T93BC 000:940.238 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:940.278 - 0.040ms returns 0
T93BC 000:940.319 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:940.351 - 0.032ms returns 0
T93BC 000:940.385 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 000:940.425 - 0.040ms returns 0
T93BC 000:940.459 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:940.492 - 0.032ms returns 0
T93BC 000:940.525 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:940.558 - 0.032ms returns 0
T93BC 000:940.592 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:940.624 - 0.032ms returns 0
T93BC 000:940.658 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:940.690 - 0.032ms returns 0
T93BC 000:940.724 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:940.758 - 0.034ms returns 0x00000007
T93BC 000:940.791 JLINK_Go()
T93BC 000:940.832   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:944.892 - 4.099ms 
T93BC 000:944.960 JLINK_IsHalted()
T93BC 000:945.789 - 0.829ms returns FALSE
T93BC 000:945.839 JLINK_HasError()
T93BC 000:953.982 JLINK_IsHalted()
T93BC 000:957.554   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:958.280 - 4.297ms returns TRUE
T93BC 000:958.329 JLINK_ReadReg(R15 (PC))
T93BC 000:958.366 - 0.036ms returns 0x20200000
T93BC 000:958.400 JLINK_ClrBPEx(BPHandle = 0x00000007)
T93BC 000:958.433 - 0.033ms returns 0x00
T93BC 000:958.469 JLINK_ReadReg(R0)
T93BC 000:958.503 - 0.034ms returns 0x00000000
T93BC 000:959.441 JLINK_HasError()
T93BC 000:959.502 JLINK_WriteReg(R0, 0x00000C00)
T93BC 000:959.540 - 0.037ms returns 0
T93BC 000:959.576 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:959.611 - 0.035ms returns 0
T93BC 000:959.654 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:959.691 - 0.037ms returns 0
T93BC 000:959.726 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:959.760 - 0.034ms returns 0
T93BC 000:959.796 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:959.830 - 0.034ms returns 0
T93BC 000:959.865 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:959.899 - 0.033ms returns 0
T93BC 000:959.935 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:959.968 - 0.033ms returns 0
T93BC 000:960.023 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:960.058 - 0.035ms returns 0
T93BC 000:960.095 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:960.129 - 0.034ms returns 0
T93BC 000:960.164 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:960.199 - 0.034ms returns 0
T93BC 000:960.234 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:960.268 - 0.034ms returns 0
T93BC 000:960.304 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:960.339 - 0.034ms returns 0
T93BC 000:960.374 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:960.408 - 0.033ms returns 0
T93BC 000:960.444 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:960.479 - 0.035ms returns 0
T93BC 000:960.515 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:960.549 - 0.034ms returns 0
T93BC 000:960.584 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 000:960.619 - 0.034ms returns 0
T93BC 000:960.654 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:960.689 - 0.034ms returns 0
T93BC 000:960.724 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:960.758 - 0.034ms returns 0
T93BC 000:960.794 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:960.828 - 0.034ms returns 0
T93BC 000:960.864 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:960.898 - 0.034ms returns 0
T93BC 000:960.935 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:960.972 - 0.037ms returns 0x00000008
T93BC 000:961.007 JLINK_Go()
T93BC 000:961.055   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:965.421 - 4.412ms 
T93BC 000:965.498 JLINK_IsHalted()
T93BC 000:969.000   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:969.780 - 4.281ms returns TRUE
T93BC 000:969.835 JLINK_ReadReg(R15 (PC))
T93BC 000:969.883 - 0.048ms returns 0x20200000
T93BC 000:969.930 JLINK_ClrBPEx(BPHandle = 0x00000008)
T93BC 000:969.976 - 0.045ms returns 0x00
T93BC 000:970.023 JLINK_ReadReg(R0)
T93BC 000:970.067 - 0.043ms returns 0x00000001
T93BC 000:970.114 JLINK_HasError()
T93BC 000:970.161 JLINK_WriteReg(R0, 0x00000C00)
T93BC 000:970.213 - 0.051ms returns 0
T93BC 000:970.259 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:970.302 - 0.043ms returns 0
T93BC 000:970.348 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:970.517 - 0.169ms returns 0
T93BC 000:970.577 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:970.624 - 0.047ms returns 0
T93BC 000:970.673 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:970.721 - 0.047ms returns 0
T93BC 000:970.771 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:970.818 - 0.047ms returns 0
T93BC 000:970.868 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:970.915 - 0.047ms returns 0
T93BC 000:970.964 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:971.008 - 0.044ms returns 0
T93BC 000:971.054 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:971.097 - 0.044ms returns 0
T93BC 000:971.143 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:971.187 - 0.044ms returns 0
T93BC 000:971.233 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:971.276 - 0.043ms returns 0
T93BC 000:971.322 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:971.365 - 0.043ms returns 0
T93BC 000:971.411 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:971.455 - 0.043ms returns 0
T93BC 000:971.520 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:971.566 - 0.046ms returns 0
T93BC 000:971.612 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:971.655 - 0.043ms returns 0
T93BC 000:971.701 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 000:971.744 - 0.044ms returns 0
T93BC 000:971.790 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:971.834 - 0.044ms returns 0
T93BC 000:971.880 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:971.923 - 0.043ms returns 0
T93BC 000:971.969 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:972.013 - 0.044ms returns 0
T93BC 000:972.144 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:972.193 - 0.049ms returns 0
T93BC 000:972.236 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:972.279 - 0.043ms returns 0x00000009
T93BC 000:972.320 JLINK_Go()
T93BC 000:972.374   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:976.485 - 4.163ms 
T93BC 000:976.545 JLINK_IsHalted()
T93BC 000:977.295 - 0.750ms returns FALSE
T93BC 000:977.343 JLINK_HasError()
T93BC 000:979.042 JLINK_IsHalted()
T93BC 000:982.846   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:983.725 - 4.681ms returns TRUE
T93BC 000:983.834 JLINK_ReadReg(R15 (PC))
T93BC 000:983.891 - 0.056ms returns 0x20200000
T93BC 000:983.943 JLINK_ClrBPEx(BPHandle = 0x00000009)
T93BC 000:983.993 - 0.050ms returns 0x00
T93BC 000:984.045 JLINK_ReadReg(R0)
T93BC 000:984.094 - 0.049ms returns 0x00000000
T93BC 000:985.500 JLINK_HasError()
T93BC 000:985.601 JLINK_WriteReg(R0, 0x00001000)
T93BC 000:985.668 - 0.066ms returns 0
T93BC 000:985.737 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:985.790 - 0.052ms returns 0
T93BC 000:985.845 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:985.898 - 0.052ms returns 0
T93BC 000:985.953 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:986.005 - 0.052ms returns 0
T93BC 000:986.060 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:986.126 - 0.066ms returns 0
T93BC 000:986.178 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:986.225 - 0.047ms returns 0
T93BC 000:986.276 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:986.325 - 0.048ms returns 0
T93BC 000:986.375 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:986.423 - 0.048ms returns 0
T93BC 000:986.473 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:986.521 - 0.048ms returns 0
T93BC 000:986.571 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:986.619 - 0.048ms returns 0
T93BC 000:986.670 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:986.718 - 0.048ms returns 0
T93BC 000:986.767 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:986.815 - 0.047ms returns 0
T93BC 000:986.871 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:986.919 - 0.048ms returns 0
T93BC 000:986.970 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:987.018 - 0.049ms returns 0
T93BC 000:987.067 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:987.116 - 0.049ms returns 0
T93BC 000:987.163 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 000:987.207 - 0.045ms returns 0
T93BC 000:987.253 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 000:987.297 - 0.044ms returns 0
T93BC 000:987.344 JLINK_WriteReg(MSP, 0x20208000)
T93BC 000:987.389 - 0.044ms returns 0
T93BC 000:987.435 JLINK_WriteReg(PSP, 0x20208000)
T93BC 000:987.495 - 0.060ms returns 0
T93BC 000:987.660 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 000:987.714 - 0.053ms returns 0
T93BC 000:987.762 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 000:987.809 - 0.048ms returns 0x0000000A
T93BC 000:987.856 JLINK_Go()
T93BC 000:987.919   CPU_ReadMem(4 bytes @ 0x********)
T93BC 000:992.147 - 4.289ms 
T93BC 000:992.234 JLINK_IsHalted()
T93BC 000:995.825   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 000:996.681 - 4.446ms returns TRUE
T93BC 000:996.755 JLINK_ReadReg(R15 (PC))
T93BC 000:996.822 - 0.066ms returns 0x20200000
T93BC 000:996.885 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T93BC 000:996.945 - 0.059ms returns 0x00
T93BC 000:997.007 JLINK_ReadReg(R0)
T93BC 000:997.066 - 0.059ms returns 0x00000001
T93BC 000:997.128 JLINK_HasError()
T93BC 000:997.189 JLINK_WriteReg(R0, 0x00001000)
T93BC 000:997.249 - 0.059ms returns 0
T93BC 000:997.312 JLINK_WriteReg(R1, 0x00000400)
T93BC 000:997.379 - 0.066ms returns 0
T93BC 000:997.446 JLINK_WriteReg(R2, 0x000000FF)
T93BC 000:997.510 - 0.064ms returns 0
T93BC 000:997.577 JLINK_WriteReg(R3, 0x00000000)
T93BC 000:997.642 - 0.065ms returns 0
T93BC 000:997.710 JLINK_WriteReg(R4, 0x00000000)
T93BC 000:997.775 - 0.065ms returns 0
T93BC 000:997.863 JLINK_WriteReg(R5, 0x00000000)
T93BC 000:997.928 - 0.065ms returns 0
T93BC 000:998.004 JLINK_WriteReg(R6, 0x00000000)
T93BC 000:998.546 - 0.541ms returns 0
T93BC 000:998.627 JLINK_WriteReg(R7, 0x00000000)
T93BC 000:998.687 - 0.061ms returns 0
T93BC 000:998.777 JLINK_WriteReg(R8, 0x00000000)
T93BC 000:998.844 - 0.067ms returns 0
T93BC 000:998.906 JLINK_WriteReg(R9, 0x20200290)
T93BC 000:999.116 - 0.208ms returns 0
T93BC 000:999.227 JLINK_WriteReg(R10, 0x00000000)
T93BC 000:999.283 - 0.057ms returns 0
T93BC 000:999.348 JLINK_WriteReg(R11, 0x00000000)
T93BC 000:999.403 - 0.055ms returns 0
T93BC 000:999.468 JLINK_WriteReg(R12, 0x00000000)
T93BC 000:999.521 - 0.053ms returns 0
T93BC 000:999.583 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 000:999.638 - 0.055ms returns 0
T93BC 000:999.701 JLINK_WriteReg(R14, 0x20200001)
T93BC 000:999.754 - 0.053ms returns 0
T93BC 000:999.814 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 000:999.889 - 0.075ms returns 0
T93BC 000:999.952 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:000.005 - 0.053ms returns 0
T93BC 001:000.068 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:000.117 - 0.050ms returns 0
T93BC 001:000.175 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:000.224 - 0.049ms returns 0
T93BC 001:000.289 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:000.339 - 0.049ms returns 0
T93BC 001:000.397 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:000.450 - 0.053ms returns 0x0000000B
T93BC 001:000.507 JLINK_Go()
T93BC 001:000.574   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:004.903 - 4.394ms 
T93BC 001:005.009 JLINK_IsHalted()
T93BC 001:005.776 - 0.766ms returns FALSE
T93BC 001:005.859 JLINK_HasError()
T93BC 001:007.943 JLINK_IsHalted()
T93BC 001:011.653   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:012.419 - 4.475ms returns TRUE
T93BC 001:012.494 JLINK_ReadReg(R15 (PC))
T93BC 001:012.542 - 0.047ms returns 0x20200000
T93BC 001:012.586 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T93BC 001:012.628 - 0.042ms returns 0x00
T93BC 001:012.678 JLINK_ReadReg(R0)
T93BC 001:012.719 - 0.041ms returns 0x00000000
T93BC 001:013.925 JLINK_HasError()
T93BC 001:014.003 JLINK_WriteReg(R0, 0x00001400)
T93BC 001:014.051 - 0.048ms returns 0
T93BC 001:014.096 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:014.166 - 0.070ms returns 0
T93BC 001:014.213 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:014.257 - 0.043ms returns 0
T93BC 001:014.303 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:014.347 - 0.044ms returns 0
T93BC 001:014.392 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:014.438 - 0.045ms returns 0
T93BC 001:014.484 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:014.527 - 0.044ms returns 0
T93BC 001:014.571 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:014.612 - 0.041ms returns 0
T93BC 001:014.800 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:014.994 - 0.194ms returns 0
T93BC 001:015.041 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:015.187 - 0.146ms returns 0
T93BC 001:015.235 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:015.275 - 0.039ms returns 0
T93BC 001:015.317 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:015.356 - 0.039ms returns 0
T93BC 001:015.396 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:015.435 - 0.039ms returns 0
T93BC 001:015.477 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:015.519 - 0.041ms returns 0
T93BC 001:015.559 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:015.599 - 0.039ms returns 0
T93BC 001:015.639 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:015.678 - 0.039ms returns 0
T93BC 001:015.719 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:015.758 - 0.039ms returns 0
T93BC 001:015.798 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:015.837 - 0.039ms returns 0
T93BC 001:015.878 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:015.917 - 0.039ms returns 0
T93BC 001:015.957 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:015.997 - 0.039ms returns 0
T93BC 001:016.035 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:016.071 - 0.036ms returns 0
T93BC 001:016.110 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:016.164 - 0.054ms returns 0x0000000C
T93BC 001:016.203 JLINK_Go()
T93BC 001:016.256   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:020.511 - 4.306ms 
T93BC 001:020.588 JLINK_IsHalted()
T93BC 001:024.303   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:025.039 - 4.451ms returns TRUE
T93BC 001:025.083 JLINK_ReadReg(R15 (PC))
T93BC 001:025.123 - 0.039ms returns 0x20200000
T93BC 001:025.162 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T93BC 001:025.199 - 0.037ms returns 0x00
T93BC 001:025.237 JLINK_ReadReg(R0)
T93BC 001:025.273 - 0.036ms returns 0x00000001
T93BC 001:025.317 JLINK_HasError()
T93BC 001:025.355 JLINK_WriteReg(R0, 0x00001400)
T93BC 001:025.393 - 0.038ms returns 0
T93BC 001:025.432 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:025.469 - 0.037ms returns 0
T93BC 001:025.507 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:025.543 - 0.036ms returns 0
T93BC 001:025.581 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:025.617 - 0.036ms returns 0
T93BC 001:025.655 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:025.692 - 0.036ms returns 0
T93BC 001:025.731 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:025.767 - 0.036ms returns 0
T93BC 001:025.805 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:025.841 - 0.036ms returns 0
T93BC 001:025.879 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:025.914 - 0.035ms returns 0
T93BC 001:025.951 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:025.988 - 0.036ms returns 0
T93BC 001:026.025 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:026.061 - 0.036ms returns 0
T93BC 001:026.099 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:026.134 - 0.035ms returns 0
T93BC 001:026.171 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:026.207 - 0.035ms returns 0
T93BC 001:026.244 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:026.281 - 0.036ms returns 0
T93BC 001:026.316 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:026.352 - 0.034ms returns 0
T93BC 001:026.388 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:026.422 - 0.034ms returns 0
T93BC 001:026.458 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:026.492 - 0.034ms returns 0
T93BC 001:026.528 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:026.562 - 0.034ms returns 0
T93BC 001:026.597 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:026.631 - 0.034ms returns 0
T93BC 001:026.668 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:026.700 - 0.032ms returns 0
T93BC 001:026.733 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:026.778 - 0.044ms returns 0
T93BC 001:026.813 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:026.846 - 0.033ms returns 0x0000000D
T93BC 001:026.880 JLINK_Go()
T93BC 001:026.920   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:031.078 - 4.197ms 
T93BC 001:031.128 JLINK_IsHalted()
T93BC 001:031.919 - 0.791ms returns FALSE
T93BC 001:031.983 JLINK_HasError()
T93BC 001:033.504 JLINK_IsHalted()
T93BC 001:037.100   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:037.851 - 4.342ms returns TRUE
T93BC 001:037.908 JLINK_ReadReg(R15 (PC))
T93BC 001:037.952 - 0.043ms returns 0x20200000
T93BC 001:038.007 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T93BC 001:038.051 - 0.044ms returns 0x00
T93BC 001:038.090 JLINK_ReadReg(R0)
T93BC 001:038.127 - 0.036ms returns 0x00000000
T93BC 001:039.114 JLINK_HasError()
T93BC 001:039.174 JLINK_WriteReg(R0, 0x00001800)
T93BC 001:039.213 - 0.039ms returns 0
T93BC 001:039.254 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:039.289 - 0.034ms returns 0
T93BC 001:039.325 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:039.359 - 0.034ms returns 0
T93BC 001:039.395 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:039.429 - 0.034ms returns 0
T93BC 001:039.465 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:039.499 - 0.034ms returns 0
T93BC 001:039.535 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:039.588 - 0.052ms returns 0
T93BC 001:039.624 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:039.658 - 0.034ms returns 0
T93BC 001:039.693 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:039.727 - 0.034ms returns 0
T93BC 001:039.763 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:039.797 - 0.034ms returns 0
T93BC 001:039.833 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:039.867 - 0.034ms returns 0
T93BC 001:039.903 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:039.937 - 0.034ms returns 0
T93BC 001:039.973 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:040.007 - 0.034ms returns 0
T93BC 001:040.042 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:040.076 - 0.034ms returns 0
T93BC 001:040.112 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:040.147 - 0.035ms returns 0
T93BC 001:040.183 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:040.217 - 0.034ms returns 0
T93BC 001:040.253 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:040.287 - 0.034ms returns 0
T93BC 001:040.323 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:040.357 - 0.034ms returns 0
T93BC 001:040.393 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:040.427 - 0.034ms returns 0
T93BC 001:040.463 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:040.497 - 0.034ms returns 0
T93BC 001:040.532 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:040.566 - 0.034ms returns 0
T93BC 001:040.603 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:040.640 - 0.037ms returns 0x0000000E
T93BC 001:040.675 JLINK_Go()
T93BC 001:040.723   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:044.821 - 4.144ms 
T93BC 001:044.877 JLINK_IsHalted()
T93BC 001:048.342   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:049.168 - 4.289ms returns TRUE
T93BC 001:049.239 JLINK_ReadReg(R15 (PC))
T93BC 001:049.280 - 0.040ms returns 0x20200000
T93BC 001:049.316 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T93BC 001:049.352 - 0.036ms returns 0x00
T93BC 001:049.388 JLINK_ReadReg(R0)
T93BC 001:049.421 - 0.033ms returns 0x00000001
T93BC 001:049.457 JLINK_HasError()
T93BC 001:049.493 JLINK_WriteReg(R0, 0x00001800)
T93BC 001:049.528 - 0.034ms returns 0
T93BC 001:049.564 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:049.597 - 0.033ms returns 0
T93BC 001:049.631 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:049.665 - 0.033ms returns 0
T93BC 001:049.700 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:049.739 - 0.039ms returns 0
T93BC 001:049.776 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:049.809 - 0.033ms returns 0
T93BC 001:049.844 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:049.877 - 0.033ms returns 0
T93BC 001:049.911 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:049.945 - 0.033ms returns 0
T93BC 001:049.980 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:050.013 - 0.033ms returns 0
T93BC 001:050.048 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:050.081 - 0.033ms returns 0
T93BC 001:050.115 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:050.148 - 0.032ms returns 0
T93BC 001:050.182 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:050.217 - 0.034ms returns 0
T93BC 001:050.254 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:050.289 - 0.035ms returns 0
T93BC 001:050.326 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:050.360 - 0.035ms returns 0
T93BC 001:050.398 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:050.433 - 0.036ms returns 0
T93BC 001:050.471 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:050.506 - 0.035ms returns 0
T93BC 001:050.543 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:050.585 - 0.042ms returns 0
T93BC 001:050.626 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:050.661 - 0.035ms returns 0
T93BC 001:050.698 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:050.734 - 0.035ms returns 0
T93BC 001:050.770 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:050.806 - 0.035ms returns 0
T93BC 001:050.843 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:050.894 - 0.051ms returns 0
T93BC 001:050.932 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:050.969 - 0.037ms returns 0x0000000F
T93BC 001:051.006 JLINK_Go()
T93BC 001:051.051   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:055.247 - 4.240ms 
T93BC 001:055.289 JLINK_IsHalted()
T93BC 001:056.012 - 0.722ms returns FALSE
T93BC 001:056.073 JLINK_HasError()
T93BC 001:063.521 JLINK_IsHalted()
T93BC 001:067.148   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:067.880 - 4.358ms returns TRUE
T93BC 001:067.926 JLINK_ReadReg(R15 (PC))
T93BC 001:067.966 - 0.040ms returns 0x20200000
T93BC 001:068.005 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T93BC 001:068.042 - 0.036ms returns 0x00
T93BC 001:068.080 JLINK_ReadReg(R0)
T93BC 001:068.118 - 0.038ms returns 0x00000000
T93BC 001:069.244 JLINK_HasError()
T93BC 001:069.311 JLINK_WriteReg(R0, 0x00001C00)
T93BC 001:069.352 - 0.040ms returns 0
T93BC 001:069.390 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:069.426 - 0.036ms returns 0
T93BC 001:069.465 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:069.501 - 0.036ms returns 0
T93BC 001:069.540 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:069.576 - 0.036ms returns 0
T93BC 001:069.614 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:069.650 - 0.036ms returns 0
T93BC 001:069.687 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:069.724 - 0.036ms returns 0
T93BC 001:069.762 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:069.799 - 0.037ms returns 0
T93BC 001:069.837 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:069.873 - 0.036ms returns 0
T93BC 001:069.911 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:069.947 - 0.036ms returns 0
T93BC 001:070.008 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:070.045 - 0.036ms returns 0
T93BC 001:070.083 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:070.119 - 0.036ms returns 0
T93BC 001:070.157 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:070.193 - 0.036ms returns 0
T93BC 001:070.231 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:070.267 - 0.036ms returns 0
T93BC 001:070.305 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:070.342 - 0.037ms returns 0
T93BC 001:070.380 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:070.414 - 0.034ms returns 0
T93BC 001:070.449 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:070.484 - 0.035ms returns 0
T93BC 001:070.519 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:070.554 - 0.034ms returns 0
T93BC 001:070.590 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:070.624 - 0.034ms returns 0
T93BC 001:070.659 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:070.694 - 0.034ms returns 0
T93BC 001:070.730 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:070.764 - 0.034ms returns 0
T93BC 001:070.801 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:070.837 - 0.037ms returns 0x00000010
T93BC 001:070.873 JLINK_Go()
T93BC 001:070.920   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:075.014 - 4.141ms 
T93BC 001:075.055 JLINK_IsHalted()
T93BC 001:078.552   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:079.320 - 4.265ms returns TRUE
T93BC 001:079.364 JLINK_ReadReg(R15 (PC))
T93BC 001:079.410 - 0.045ms returns 0x20200000
T93BC 001:079.448 JLINK_ClrBPEx(BPHandle = 0x00000010)
T93BC 001:079.485 - 0.037ms returns 0x00
T93BC 001:079.524 JLINK_ReadReg(R0)
T93BC 001:079.561 - 0.037ms returns 0x00000001
T93BC 001:079.599 JLINK_HasError()
T93BC 001:079.639 JLINK_WriteReg(R0, 0x00001C00)
T93BC 001:079.676 - 0.037ms returns 0
T93BC 001:079.714 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:079.750 - 0.036ms returns 0
T93BC 001:079.788 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:079.824 - 0.036ms returns 0
T93BC 001:079.862 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:079.898 - 0.036ms returns 0
T93BC 001:079.936 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:079.980 - 0.044ms returns 0
T93BC 001:080.024 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:080.060 - 0.036ms returns 0
T93BC 001:080.098 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:080.134 - 0.036ms returns 0
T93BC 001:080.171 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:080.208 - 0.036ms returns 0
T93BC 001:080.245 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:080.281 - 0.036ms returns 0
T93BC 001:080.319 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:080.355 - 0.036ms returns 0
T93BC 001:080.393 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:080.429 - 0.036ms returns 0
T93BC 001:080.467 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:080.503 - 0.036ms returns 0
T93BC 001:080.541 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:080.578 - 0.037ms returns 0
T93BC 001:080.616 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:080.652 - 0.036ms returns 0
T93BC 001:080.691 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:080.727 - 0.036ms returns 0
T93BC 001:080.764 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:080.800 - 0.036ms returns 0
T93BC 001:080.838 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:080.875 - 0.036ms returns 0
T93BC 001:080.911 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:080.947 - 0.036ms returns 0
T93BC 001:080.985 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:081.021 - 0.036ms returns 0
T93BC 001:081.073 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:081.109 - 0.036ms returns 0
T93BC 001:081.145 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:081.180 - 0.035ms returns 0x00000011
T93BC 001:081.292 JLINK_Go()
T93BC 001:081.372   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:085.488 - 4.195ms 
T93BC 001:085.558 JLINK_IsHalted()
T93BC 001:086.281 - 0.722ms returns FALSE
T93BC 001:086.331 JLINK_HasError()
T93BC 001:089.595 JLINK_IsHalted()
T93BC 001:093.230   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:094.042 - 4.446ms returns TRUE
T93BC 001:094.096 JLINK_ReadReg(R15 (PC))
T93BC 001:094.133 - 0.037ms returns 0x20200000
T93BC 001:094.169 JLINK_ClrBPEx(BPHandle = 0x00000011)
T93BC 001:094.209 - 0.040ms returns 0x00
T93BC 001:094.244 JLINK_ReadReg(R0)
T93BC 001:094.278 - 0.034ms returns 0x00000000
T93BC 001:095.137 JLINK_HasError()
T93BC 001:095.195 JLINK_WriteReg(R0, 0x00002000)
T93BC 001:095.233 - 0.037ms returns 0
T93BC 001:095.269 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:095.305 - 0.036ms returns 0
T93BC 001:095.340 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:095.374 - 0.034ms returns 0
T93BC 001:095.410 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:095.444 - 0.034ms returns 0
T93BC 001:095.480 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:095.514 - 0.034ms returns 0
T93BC 001:095.549 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:095.583 - 0.034ms returns 0
T93BC 001:095.619 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:095.724 - 0.105ms returns 0
T93BC 001:095.761 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:095.811 - 0.050ms returns 0
T93BC 001:095.846 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:095.880 - 0.034ms returns 0
T93BC 001:095.915 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:095.949 - 0.034ms returns 0
T93BC 001:095.985 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:096.019 - 0.034ms returns 0
T93BC 001:096.054 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:096.088 - 0.033ms returns 0
T93BC 001:096.125 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:096.159 - 0.034ms returns 0
T93BC 001:096.195 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:096.229 - 0.034ms returns 0
T93BC 001:096.265 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:096.299 - 0.034ms returns 0
T93BC 001:096.334 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:096.368 - 0.034ms returns 0
T93BC 001:096.404 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:096.438 - 0.034ms returns 0
T93BC 001:096.473 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:096.507 - 0.034ms returns 0
T93BC 001:096.543 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:096.577 - 0.034ms returns 0
T93BC 001:096.612 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:096.646 - 0.033ms returns 0
T93BC 001:096.682 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:096.719 - 0.037ms returns 0x00000012
T93BC 001:096.762 JLINK_Go()
T93BC 001:096.826   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:101.161 - 4.397ms 
T93BC 001:101.231 JLINK_IsHalted()
T93BC 001:104.690   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:105.456 - 4.224ms returns TRUE
T93BC 001:105.516 JLINK_ReadReg(R15 (PC))
T93BC 001:105.558 - 0.041ms returns 0x20200000
T93BC 001:105.597 JLINK_ClrBPEx(BPHandle = 0x00000012)
T93BC 001:105.634 - 0.037ms returns 0x00
T93BC 001:105.673 JLINK_ReadReg(R0)
T93BC 001:105.709 - 0.036ms returns 0x00000001
T93BC 001:105.748 JLINK_HasError()
T93BC 001:105.787 JLINK_WriteReg(R0, 0x00002000)
T93BC 001:105.829 - 0.042ms returns 0
T93BC 001:105.867 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:105.903 - 0.036ms returns 0
T93BC 001:105.940 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:105.976 - 0.035ms returns 0
T93BC 001:106.012 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:106.048 - 0.035ms returns 0
T93BC 001:106.084 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:106.119 - 0.035ms returns 0
T93BC 001:106.156 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:106.192 - 0.036ms returns 0
T93BC 001:106.229 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:106.264 - 0.035ms returns 0
T93BC 001:106.301 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:106.336 - 0.035ms returns 0
T93BC 001:106.373 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:106.410 - 0.036ms returns 0
T93BC 001:106.446 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:106.481 - 0.034ms returns 0
T93BC 001:106.517 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:106.552 - 0.035ms returns 0
T93BC 001:106.589 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:106.624 - 0.035ms returns 0
T93BC 001:106.661 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:106.696 - 0.035ms returns 0
T93BC 001:106.733 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:106.768 - 0.035ms returns 0
T93BC 001:106.805 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:106.841 - 0.035ms returns 0
T93BC 001:106.877 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:106.913 - 0.035ms returns 0
T93BC 001:106.949 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:106.984 - 0.035ms returns 0
T93BC 001:107.021 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:107.056 - 0.035ms returns 0
T93BC 001:107.093 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:107.128 - 0.035ms returns 0
T93BC 001:107.181 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:107.217 - 0.036ms returns 0
T93BC 001:107.255 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:107.291 - 0.037ms returns 0x00000013
T93BC 001:107.329 JLINK_Go()
T93BC 001:107.377   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:111.538 - 4.207ms 
T93BC 001:111.584 JLINK_IsHalted()
T93BC 001:112.439 - 0.854ms returns FALSE
T93BC 001:112.488 JLINK_HasError()
T93BC 001:113.627 JLINK_IsHalted()
T93BC 001:114.444 - 0.817ms returns FALSE
T93BC 001:114.494 JLINK_HasError()
T93BC 001:116.579 JLINK_IsHalted()
T93BC 001:120.024   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:120.772 - 4.191ms returns TRUE
T93BC 001:120.856 JLINK_ReadReg(R15 (PC))
T93BC 001:120.900 - 0.044ms returns 0x20200000
T93BC 001:120.938 JLINK_ClrBPEx(BPHandle = 0x00000013)
T93BC 001:120.976 - 0.037ms returns 0x00
T93BC 001:121.013 JLINK_ReadReg(R0)
T93BC 001:121.050 - 0.037ms returns 0x00000000
T93BC 001:122.071 JLINK_HasError()
T93BC 001:122.135 JLINK_WriteReg(R0, 0x00002400)
T93BC 001:122.173 - 0.038ms returns 0
T93BC 001:122.209 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:122.242 - 0.033ms returns 0
T93BC 001:122.289 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:122.324 - 0.035ms returns 0
T93BC 001:122.359 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:122.393 - 0.034ms returns 0
T93BC 001:122.429 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:122.463 - 0.034ms returns 0
T93BC 001:122.517 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:122.552 - 0.035ms returns 0
T93BC 001:122.587 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:122.620 - 0.033ms returns 0
T93BC 001:122.655 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:122.689 - 0.033ms returns 0
T93BC 001:122.724 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:122.757 - 0.032ms returns 0
T93BC 001:122.792 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:122.838 - 0.046ms returns 0
T93BC 001:122.873 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:122.907 - 0.033ms returns 0
T93BC 001:122.942 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:122.975 - 0.033ms returns 0
T93BC 001:123.011 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:123.044 - 0.033ms returns 0
T93BC 001:123.079 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:123.113 - 0.035ms returns 0
T93BC 001:123.148 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:123.182 - 0.033ms returns 0
T93BC 001:123.217 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:123.252 - 0.034ms returns 0
T93BC 001:123.287 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:123.321 - 0.034ms returns 0
T93BC 001:123.357 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:123.391 - 0.034ms returns 0
T93BC 001:123.426 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:123.459 - 0.033ms returns 0
T93BC 001:123.494 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:123.527 - 0.033ms returns 0
T93BC 001:123.564 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:123.600 - 0.037ms returns 0x00000014
T93BC 001:123.635 JLINK_Go()
T93BC 001:123.684   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:127.796 - 4.160ms 
T93BC 001:127.869 JLINK_IsHalted()
T93BC 001:131.502   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:132.237 - 4.366ms returns TRUE
T93BC 001:132.308 JLINK_ReadReg(R15 (PC))
T93BC 001:132.345 - 0.036ms returns 0x20200000
T93BC 001:132.379 JLINK_ClrBPEx(BPHandle = 0x00000014)
T93BC 001:132.412 - 0.033ms returns 0x00
T93BC 001:132.446 JLINK_ReadReg(R0)
T93BC 001:132.479 - 0.032ms returns 0x00000001
T93BC 001:132.513 JLINK_HasError()
T93BC 001:132.547 JLINK_WriteReg(R0, 0x00002400)
T93BC 001:132.580 - 0.033ms returns 0
T93BC 001:132.614 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:132.646 - 0.032ms returns 0
T93BC 001:132.679 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:132.711 - 0.031ms returns 0
T93BC 001:132.743 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:132.775 - 0.031ms returns 0
T93BC 001:132.808 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:132.840 - 0.031ms returns 0
T93BC 001:132.873 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:132.904 - 0.031ms returns 0
T93BC 001:132.937 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:132.969 - 0.031ms returns 0
T93BC 001:133.001 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:133.033 - 0.031ms returns 0
T93BC 001:133.067 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:133.098 - 0.032ms returns 0
T93BC 001:133.132 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:133.164 - 0.031ms returns 0
T93BC 001:133.197 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:133.228 - 0.031ms returns 0
T93BC 001:133.259 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:133.289 - 0.030ms returns 0
T93BC 001:133.321 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:133.350 - 0.029ms returns 0
T93BC 001:133.382 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:133.412 - 0.030ms returns 0
T93BC 001:133.443 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:133.474 - 0.030ms returns 0
T93BC 001:133.505 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:133.535 - 0.030ms returns 0
T93BC 001:133.566 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:133.597 - 0.030ms returns 0
T93BC 001:133.628 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:133.658 - 0.029ms returns 0
T93BC 001:133.689 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:133.719 - 0.030ms returns 0
T93BC 001:133.749 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:133.779 - 0.029ms returns 0
T93BC 001:133.811 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:133.842 - 0.031ms returns 0x00000015
T93BC 001:133.873 JLINK_Go()
T93BC 001:133.912   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:137.936 - 4.062ms 
T93BC 001:137.985 JLINK_IsHalted()
T93BC 001:138.701 - 0.714ms returns FALSE
T93BC 001:138.751 JLINK_HasError()
T93BC 001:140.073 JLINK_IsHalted()
T93BC 001:143.629   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:144.410 - 4.336ms returns TRUE
T93BC 001:144.458 JLINK_ReadReg(R15 (PC))
T93BC 001:144.503 - 0.045ms returns 0x20200000
T93BC 001:144.546 JLINK_ClrBPEx(BPHandle = 0x00000015)
T93BC 001:144.597 - 0.051ms returns 0x00
T93BC 001:144.646 JLINK_ReadReg(R0)
T93BC 001:144.687 - 0.041ms returns 0x00000000
T93BC 001:145.836 JLINK_HasError()
T93BC 001:145.912 JLINK_WriteReg(R0, 0x00002800)
T93BC 001:145.962 - 0.049ms returns 0
T93BC 001:146.007 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:146.047 - 0.040ms returns 0
T93BC 001:146.090 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:146.157 - 0.067ms returns 0
T93BC 001:146.201 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:146.241 - 0.040ms returns 0
T93BC 001:146.283 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:146.322 - 0.039ms returns 0
T93BC 001:146.362 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:146.400 - 0.037ms returns 0
T93BC 001:146.439 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:146.476 - 0.037ms returns 0
T93BC 001:146.515 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:146.554 - 0.039ms returns 0
T93BC 001:146.594 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:146.632 - 0.038ms returns 0
T93BC 001:146.671 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:146.708 - 0.037ms returns 0
T93BC 001:146.747 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:146.785 - 0.038ms returns 0
T93BC 001:146.825 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:146.863 - 0.038ms returns 0
T93BC 001:146.902 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:146.940 - 0.038ms returns 0
T93BC 001:146.979 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:147.018 - 0.039ms returns 0
T93BC 001:147.058 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:147.096 - 0.038ms returns 0
T93BC 001:147.150 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:147.186 - 0.036ms returns 0
T93BC 001:147.223 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:147.259 - 0.036ms returns 0
T93BC 001:147.295 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:147.331 - 0.035ms returns 0
T93BC 001:147.368 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:147.403 - 0.035ms returns 0
T93BC 001:147.440 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:147.475 - 0.035ms returns 0
T93BC 001:147.514 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:147.549 - 0.036ms returns 0x00000016
T93BC 001:147.584 JLINK_Go()
T93BC 001:147.629   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:151.893 - 4.308ms 
T93BC 001:151.960 JLINK_IsHalted()
T93BC 001:155.525   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:156.388 - 4.427ms returns TRUE
T93BC 001:156.442 JLINK_ReadReg(R15 (PC))
T93BC 001:156.488 - 0.046ms returns 0x20200000
T93BC 001:156.538 JLINK_ClrBPEx(BPHandle = 0x00000016)
T93BC 001:156.580 - 0.042ms returns 0x00
T93BC 001:156.623 JLINK_ReadReg(R0)
T93BC 001:156.664 - 0.041ms returns 0x00000001
T93BC 001:156.706 JLINK_HasError()
T93BC 001:156.750 JLINK_WriteReg(R0, 0x00002800)
T93BC 001:156.793 - 0.042ms returns 0
T93BC 001:156.835 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:156.876 - 0.040ms returns 0
T93BC 001:156.919 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:156.960 - 0.041ms returns 0
T93BC 001:157.002 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:157.043 - 0.040ms returns 0
T93BC 001:157.084 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:157.125 - 0.041ms returns 0
T93BC 001:157.212 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:157.254 - 0.042ms returns 0
T93BC 001:157.296 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:157.337 - 0.040ms returns 0
T93BC 001:157.378 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:157.418 - 0.039ms returns 0
T93BC 001:157.460 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:157.500 - 0.039ms returns 0
T93BC 001:157.542 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:157.583 - 0.040ms returns 0
T93BC 001:157.625 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:157.665 - 0.040ms returns 0
T93BC 001:157.708 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:157.748 - 0.040ms returns 0
T93BC 001:157.790 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:157.831 - 0.040ms returns 0
T93BC 001:157.873 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:157.914 - 0.041ms returns 0
T93BC 001:157.953 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:157.991 - 0.038ms returns 0
T93BC 001:158.031 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:158.069 - 0.038ms returns 0
T93BC 001:158.109 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:158.176 - 0.067ms returns 0
T93BC 001:158.216 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:158.255 - 0.038ms returns 0
T93BC 001:158.294 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:158.332 - 0.037ms returns 0
T93BC 001:158.371 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:158.408 - 0.037ms returns 0
T93BC 001:158.449 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:158.488 - 0.040ms returns 0x00000017
T93BC 001:158.528 JLINK_Go()
T93BC 001:158.574   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:162.677 - 4.148ms 
T93BC 001:162.722 JLINK_IsHalted()
T93BC 001:163.410 - 0.687ms returns FALSE
T93BC 001:163.470 JLINK_HasError()
T93BC 001:167.541 JLINK_IsHalted()
T93BC 001:171.226   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:172.027 - 4.485ms returns TRUE
T93BC 001:172.079 JLINK_ReadReg(R15 (PC))
T93BC 001:172.125 - 0.045ms returns 0x20200000
T93BC 001:172.168 JLINK_ClrBPEx(BPHandle = 0x00000017)
T93BC 001:172.211 - 0.042ms returns 0x00
T93BC 001:172.255 JLINK_ReadReg(R0)
T93BC 001:172.299 - 0.044ms returns 0x00000000
T93BC 001:173.528 JLINK_HasError()
T93BC 001:173.612 JLINK_WriteReg(R0, 0x00002C00)
T93BC 001:173.663 - 0.051ms returns 0
T93BC 001:173.709 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:173.753 - 0.043ms returns 0
T93BC 001:173.798 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:173.841 - 0.043ms returns 0
T93BC 001:173.887 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:173.931 - 0.044ms returns 0
T93BC 001:173.976 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:174.019 - 0.043ms returns 0
T93BC 001:174.065 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:174.108 - 0.043ms returns 0
T93BC 001:174.150 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:174.191 - 0.040ms returns 0
T93BC 001:174.233 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:174.273 - 0.040ms returns 0
T93BC 001:174.315 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:174.356 - 0.041ms returns 0
T93BC 001:174.397 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:174.447 - 0.049ms returns 0
T93BC 001:174.486 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:174.525 - 0.038ms returns 0
T93BC 001:174.565 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:174.602 - 0.037ms returns 0
T93BC 001:174.642 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:174.680 - 0.037ms returns 0
T93BC 001:174.720 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:174.759 - 0.039ms returns 0
T93BC 001:174.799 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:174.836 - 0.037ms returns 0
T93BC 001:174.876 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:174.914 - 0.038ms returns 0
T93BC 001:174.953 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:174.991 - 0.038ms returns 0
T93BC 001:175.031 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:175.069 - 0.038ms returns 0
T93BC 001:175.109 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:175.149 - 0.040ms returns 0
T93BC 001:175.185 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:175.221 - 0.035ms returns 0
T93BC 001:175.259 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:175.297 - 0.038ms returns 0x00000018
T93BC 001:175.334 JLINK_Go()
T93BC 001:175.382   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:179.488 - 4.153ms 
T93BC 001:179.543 JLINK_IsHalted()
T93BC 001:182.973   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:183.713 - 4.168ms returns TRUE
T93BC 001:183.768 JLINK_ReadReg(R15 (PC))
T93BC 001:183.809 - 0.040ms returns 0x20200000
T93BC 001:183.848 JLINK_ClrBPEx(BPHandle = 0x00000018)
T93BC 001:183.886 - 0.037ms returns 0x00
T93BC 001:183.923 JLINK_ReadReg(R0)
T93BC 001:183.959 - 0.035ms returns 0x00000001
T93BC 001:183.997 JLINK_HasError()
T93BC 001:184.036 JLINK_WriteReg(R0, 0x00002C00)
T93BC 001:184.074 - 0.037ms returns 0
T93BC 001:184.112 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:184.149 - 0.036ms returns 0
T93BC 001:184.187 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:184.223 - 0.036ms returns 0
T93BC 001:184.261 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:184.297 - 0.036ms returns 0
T93BC 001:184.335 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:184.371 - 0.036ms returns 0
T93BC 001:184.409 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:184.463 - 0.054ms returns 0
T93BC 001:184.506 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:184.542 - 0.036ms returns 0
T93BC 001:184.580 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:184.616 - 0.036ms returns 0
T93BC 001:184.654 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:184.690 - 0.036ms returns 0
T93BC 001:184.728 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:184.764 - 0.036ms returns 0
T93BC 001:184.802 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:184.839 - 0.037ms returns 0
T93BC 001:184.877 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:184.914 - 0.036ms returns 0
T93BC 001:184.952 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:184.988 - 0.036ms returns 0
T93BC 001:185.025 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:185.062 - 0.037ms returns 0
T93BC 001:185.100 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:185.136 - 0.036ms returns 0
T93BC 001:185.174 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:185.211 - 0.036ms returns 0
T93BC 001:185.248 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:185.285 - 0.036ms returns 0
T93BC 001:185.323 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:185.358 - 0.036ms returns 0
T93BC 001:185.394 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:185.501 - 0.106ms returns 0
T93BC 001:185.539 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:185.572 - 0.034ms returns 0
T93BC 001:185.609 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:185.645 - 0.036ms returns 0x00000019
T93BC 001:185.681 JLINK_Go()
T93BC 001:185.725   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:189.705 - 4.024ms 
T93BC 001:189.770 JLINK_IsHalted()
T93BC 001:190.454 - 0.684ms returns FALSE
T93BC 001:190.500 JLINK_HasError()
T93BC 001:193.488 JLINK_IsHalted()
T93BC 001:197.016   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:197.966 - 4.476ms returns TRUE
T93BC 001:198.055 JLINK_ReadReg(R15 (PC))
T93BC 001:198.100 - 0.045ms returns 0x20200000
T93BC 001:198.141 JLINK_ClrBPEx(BPHandle = 0x00000019)
T93BC 001:198.181 - 0.040ms returns 0x00
T93BC 001:198.228 JLINK_ReadReg(R0)
T93BC 001:198.267 - 0.038ms returns 0x00000000
T93BC 001:200.558 JLINK_HasError()
T93BC 001:200.636 JLINK_WriteReg(R0, 0x00003000)
T93BC 001:200.675 - 0.039ms returns 0
T93BC 001:200.712 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:200.746 - 0.034ms returns 0
T93BC 001:200.780 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:200.814 - 0.033ms returns 0
T93BC 001:200.848 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:200.882 - 0.034ms returns 0
T93BC 001:200.917 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:200.950 - 0.033ms returns 0
T93BC 001:200.985 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:201.018 - 0.033ms returns 0
T93BC 001:201.054 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:201.087 - 0.033ms returns 0
T93BC 001:201.122 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:201.155 - 0.033ms returns 0
T93BC 001:201.190 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:201.223 - 0.033ms returns 0
T93BC 001:201.258 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:201.289 - 0.031ms returns 0
T93BC 001:201.321 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:201.353 - 0.031ms returns 0
T93BC 001:201.386 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:201.417 - 0.031ms returns 0
T93BC 001:201.450 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:201.481 - 0.031ms returns 0
T93BC 001:201.524 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:201.555 - 0.031ms returns 0
T93BC 001:201.586 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:201.616 - 0.030ms returns 0
T93BC 001:201.647 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:201.678 - 0.030ms returns 0
T93BC 001:201.709 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:201.739 - 0.030ms returns 0
T93BC 001:201.771 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:201.801 - 0.030ms returns 0
T93BC 001:201.832 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:201.862 - 0.030ms returns 0
T93BC 001:201.893 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:201.923 - 0.029ms returns 0
T93BC 001:201.962 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:201.995 - 0.033ms returns 0x0000001A
T93BC 001:202.026 JLINK_Go()
T93BC 001:202.073   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:206.354 - 4.323ms 
T93BC 001:206.398 JLINK_IsHalted()
T93BC 001:209.957   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:210.671 - 4.272ms returns TRUE
T93BC 001:210.718 JLINK_ReadReg(R15 (PC))
T93BC 001:210.760 - 0.042ms returns 0x20200000
T93BC 001:210.802 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T93BC 001:210.841 - 0.039ms returns 0x00
T93BC 001:210.882 JLINK_ReadReg(R0)
T93BC 001:210.921 - 0.039ms returns 0x00000001
T93BC 001:210.963 JLINK_HasError()
T93BC 001:211.004 JLINK_WriteReg(R0, 0x00003000)
T93BC 001:211.045 - 0.040ms returns 0
T93BC 001:211.085 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:211.124 - 0.039ms returns 0
T93BC 001:211.164 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:211.203 - 0.039ms returns 0
T93BC 001:211.244 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:211.283 - 0.039ms returns 0
T93BC 001:211.323 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:211.362 - 0.038ms returns 0
T93BC 001:211.402 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:211.440 - 0.038ms returns 0
T93BC 001:211.480 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:211.527 - 0.046ms returns 0
T93BC 001:211.566 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:211.604 - 0.038ms returns 0
T93BC 001:211.645 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:211.683 - 0.038ms returns 0
T93BC 001:211.723 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:211.762 - 0.038ms returns 0
T93BC 001:211.802 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:211.840 - 0.038ms returns 0
T93BC 001:211.881 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:211.919 - 0.038ms returns 0
T93BC 001:211.960 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:211.998 - 0.038ms returns 0
T93BC 001:212.039 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:212.078 - 0.039ms returns 0
T93BC 001:212.115 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:212.151 - 0.036ms returns 0
T93BC 001:212.189 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:212.225 - 0.036ms returns 0
T93BC 001:212.264 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:212.300 - 0.036ms returns 0
T93BC 001:212.337 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:212.373 - 0.035ms returns 0
T93BC 001:212.411 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:212.447 - 0.036ms returns 0
T93BC 001:212.484 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:212.541 - 0.056ms returns 0
T93BC 001:212.580 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:212.618 - 0.038ms returns 0x0000001B
T93BC 001:212.655 JLINK_Go()
T93BC 001:212.700   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:216.875 - 4.218ms 
T93BC 001:216.946 JLINK_IsHalted()
T93BC 001:217.670 - 0.722ms returns FALSE
T93BC 001:217.747 JLINK_HasError()
T93BC 001:224.567 JLINK_IsHalted()
T93BC 001:228.271   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:229.047 - 4.478ms returns TRUE
T93BC 001:229.141 JLINK_ReadReg(R15 (PC))
T93BC 001:229.188 - 0.046ms returns 0x20200000
T93BC 001:229.229 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T93BC 001:229.269 - 0.040ms returns 0x00
T93BC 001:229.310 JLINK_ReadReg(R0)
T93BC 001:229.349 - 0.038ms returns 0x00000000
T93BC 001:230.521 JLINK_HasError()
T93BC 001:230.629 JLINK_WriteReg(R0, 0x00003400)
T93BC 001:230.685 - 0.056ms returns 0
T93BC 001:230.727 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:230.766 - 0.038ms returns 0
T93BC 001:230.806 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:230.845 - 0.039ms returns 0
T93BC 001:230.886 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:230.924 - 0.038ms returns 0
T93BC 001:230.964 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:231.002 - 0.038ms returns 0
T93BC 001:231.041 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:231.078 - 0.036ms returns 0
T93BC 001:231.116 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:231.152 - 0.036ms returns 0
T93BC 001:231.189 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:231.464 - 0.273ms returns 0
T93BC 001:231.505 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:231.541 - 0.036ms returns 0
T93BC 001:231.577 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:231.612 - 0.035ms returns 0
T93BC 001:231.648 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:231.682 - 0.034ms returns 0
T93BC 001:231.718 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:231.766 - 0.048ms returns 0
T93BC 001:231.802 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:231.845 - 0.043ms returns 0
T93BC 001:231.882 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:231.916 - 0.035ms returns 0
T93BC 001:231.951 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:231.986 - 0.034ms returns 0
T93BC 001:232.019 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:232.051 - 0.032ms returns 0
T93BC 001:232.084 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:232.116 - 0.032ms returns 0
T93BC 001:232.149 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:232.181 - 0.031ms returns 0
T93BC 001:232.214 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:232.246 - 0.032ms returns 0
T93BC 001:232.279 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:232.313 - 0.033ms returns 0
T93BC 001:232.346 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:232.379 - 0.033ms returns 0x0000001C
T93BC 001:232.411 JLINK_Go()
T93BC 001:232.454   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:236.535 - 4.122ms 
T93BC 001:236.603 JLINK_IsHalted()
T93BC 001:240.069   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:240.846 - 4.241ms returns TRUE
T93BC 001:240.905 JLINK_ReadReg(R15 (PC))
T93BC 001:240.957 - 0.052ms returns 0x20200000
T93BC 001:241.008 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T93BC 001:241.069 - 0.061ms returns 0x00
T93BC 001:241.118 JLINK_ReadReg(R0)
T93BC 001:241.163 - 0.044ms returns 0x00000001
T93BC 001:241.210 JLINK_HasError()
T93BC 001:241.257 JLINK_WriteReg(R0, 0x00003400)
T93BC 001:241.304 - 0.046ms returns 0
T93BC 001:241.350 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:241.394 - 0.043ms returns 0
T93BC 001:241.440 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:241.484 - 0.044ms returns 0
T93BC 001:241.529 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:241.574 - 0.044ms returns 0
T93BC 001:241.619 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:241.662 - 0.043ms returns 0
T93BC 001:241.709 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:241.753 - 0.044ms returns 0
T93BC 001:241.799 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:241.843 - 0.044ms returns 0
T93BC 001:241.888 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:241.932 - 0.043ms returns 0
T93BC 001:241.972 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:242.002 - 0.030ms returns 0
T93BC 001:242.033 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:242.063 - 0.030ms returns 0
T93BC 001:242.095 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:242.125 - 0.030ms returns 0
T93BC 001:242.157 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:242.187 - 0.030ms returns 0
T93BC 001:242.218 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:242.248 - 0.030ms returns 0
T93BC 001:242.280 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:242.312 - 0.032ms returns 0
T93BC 001:242.345 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:242.377 - 0.031ms returns 0
T93BC 001:242.410 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:242.442 - 0.032ms returns 0
T93BC 001:242.475 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:242.507 - 0.032ms returns 0
T93BC 001:242.540 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:242.585 - 0.045ms returns 0
T93BC 001:242.619 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:242.651 - 0.032ms returns 0
T93BC 001:242.685 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:242.717 - 0.032ms returns 0
T93BC 001:242.760 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:242.793 - 0.034ms returns 0x0000001D
T93BC 001:242.831 JLINK_Go()
T93BC 001:242.872   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:247.006 - 4.173ms 
T93BC 001:247.091 JLINK_IsHalted()
T93BC 001:247.878 - 0.786ms returns FALSE
T93BC 001:247.956 JLINK_HasError()
T93BC 001:249.687 JLINK_IsHalted()
T93BC 001:253.188   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:253.919 - 4.219ms returns TRUE
T93BC 001:253.969 JLINK_ReadReg(R15 (PC))
T93BC 001:254.009 - 0.039ms returns 0x20200000
T93BC 001:254.045 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T93BC 001:254.080 - 0.035ms returns 0x00
T93BC 001:254.116 JLINK_ReadReg(R0)
T93BC 001:254.151 - 0.034ms returns 0x00000000
T93BC 001:255.093 JLINK_HasError()
T93BC 001:255.158 JLINK_WriteReg(R0, 0x00003800)
T93BC 001:255.215 - 0.056ms returns 0
T93BC 001:255.251 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:255.287 - 0.036ms returns 0
T93BC 001:255.323 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:255.357 - 0.034ms returns 0
T93BC 001:255.393 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:255.428 - 0.034ms returns 0
T93BC 001:255.463 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:255.498 - 0.034ms returns 0
T93BC 001:255.533 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:255.567 - 0.034ms returns 0
T93BC 001:255.604 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:255.671 - 0.067ms returns 0
T93BC 001:255.709 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:255.743 - 0.034ms returns 0
T93BC 001:255.779 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:255.812 - 0.034ms returns 0
T93BC 001:255.848 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:255.882 - 0.034ms returns 0
T93BC 001:255.917 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:255.951 - 0.034ms returns 0
T93BC 001:255.987 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:256.021 - 0.034ms returns 0
T93BC 001:256.056 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:256.090 - 0.034ms returns 0
T93BC 001:256.125 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:256.161 - 0.036ms returns 0
T93BC 001:256.197 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:256.232 - 0.034ms returns 0
T93BC 001:256.267 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:256.302 - 0.035ms returns 0
T93BC 001:256.338 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:256.372 - 0.034ms returns 0
T93BC 001:256.407 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:256.442 - 0.034ms returns 0
T93BC 001:256.477 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:256.511 - 0.033ms returns 0
T93BC 001:256.547 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:256.581 - 0.034ms returns 0
T93BC 001:256.618 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:256.654 - 0.037ms returns 0x0000001E
T93BC 001:256.690 JLINK_Go()
T93BC 001:256.739   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:260.772 - 4.081ms 
T93BC 001:260.813 JLINK_IsHalted()
T93BC 001:264.328   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:265.057 - 4.242ms returns TRUE
T93BC 001:265.104 JLINK_ReadReg(R15 (PC))
T93BC 001:265.136 - 0.031ms returns 0x20200000
T93BC 001:265.166 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T93BC 001:265.194 - 0.028ms returns 0x00
T93BC 001:265.225 JLINK_ReadReg(R0)
T93BC 001:265.252 - 0.029ms returns 0x00000001
T93BC 001:265.281 JLINK_HasError()
T93BC 001:265.311 JLINK_WriteReg(R0, 0x00003800)
T93BC 001:265.338 - 0.028ms returns 0
T93BC 001:265.367 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:265.394 - 0.026ms returns 0
T93BC 001:265.422 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:265.449 - 0.027ms returns 0
T93BC 001:265.477 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:265.505 - 0.027ms returns 0
T93BC 001:265.533 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:265.560 - 0.026ms returns 0
T93BC 001:265.589 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:265.615 - 0.026ms returns 0
T93BC 001:265.679 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:265.718 - 0.038ms returns 0
T93BC 001:265.747 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:265.775 - 0.027ms returns 0
T93BC 001:265.804 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:265.832 - 0.028ms returns 0
T93BC 001:265.860 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:265.888 - 0.028ms returns 0
T93BC 001:265.917 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:265.945 - 0.027ms returns 0
T93BC 001:265.973 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:266.000 - 0.026ms returns 0
T93BC 001:266.028 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:266.054 - 0.026ms returns 0
T93BC 001:266.095 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:266.123 - 0.041ms returns 0
T93BC 001:266.151 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:266.176 - 0.025ms returns 0
T93BC 001:266.213 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:266.240 - 0.026ms returns 0
T93BC 001:266.267 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:266.294 - 0.027ms returns 0
T93BC 001:266.320 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:266.345 - 0.025ms returns 0
T93BC 001:266.371 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:266.405 - 0.033ms returns 0
T93BC 001:266.430 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:266.455 - 0.024ms returns 0
T93BC 001:266.482 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:266.509 - 0.027ms returns 0x0000001F
T93BC 001:266.535 JLINK_Go()
T93BC 001:266.570   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:270.632 - 4.096ms 
T93BC 001:270.704 JLINK_IsHalted()
T93BC 001:271.374 - 0.668ms returns FALSE
T93BC 001:271.413 JLINK_HasError()
T93BC 001:274.547 JLINK_IsHalted()
T93BC 001:278.089   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:278.825 - 4.277ms returns TRUE
T93BC 001:278.880 JLINK_ReadReg(R15 (PC))
T93BC 001:278.929 - 0.049ms returns 0x20200000
T93BC 001:278.976 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T93BC 001:279.021 - 0.044ms returns 0x00
T93BC 001:279.068 JLINK_ReadReg(R0)
T93BC 001:279.113 - 0.045ms returns 0x00000000
T93BC 001:280.157 JLINK_HasError()
T93BC 001:280.225 JLINK_WriteReg(R0, 0x00003C00)
T93BC 001:280.268 - 0.043ms returns 0
T93BC 001:280.309 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:280.348 - 0.039ms returns 0
T93BC 001:280.389 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:280.427 - 0.039ms returns 0
T93BC 001:280.468 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:280.506 - 0.038ms returns 0
T93BC 001:280.572 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:280.612 - 0.039ms returns 0
T93BC 001:280.652 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:280.690 - 0.038ms returns 0
T93BC 001:280.732 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:280.771 - 0.039ms returns 0
T93BC 001:280.837 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:280.876 - 0.039ms returns 0
T93BC 001:280.917 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:280.956 - 0.039ms returns 0
T93BC 001:280.996 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:281.032 - 0.036ms returns 0
T93BC 001:281.070 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:281.106 - 0.036ms returns 0
T93BC 001:281.144 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:281.180 - 0.036ms returns 0
T93BC 001:281.218 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:281.255 - 0.036ms returns 0
T93BC 001:281.470 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:281.515 - 0.044ms returns 0
T93BC 001:281.553 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:281.625 - 0.071ms returns 0
T93BC 001:281.670 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:281.706 - 0.036ms returns 0
T93BC 001:281.744 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:281.780 - 0.036ms returns 0
T93BC 001:281.818 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:281.854 - 0.036ms returns 0
T93BC 001:281.902 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:281.941 - 0.038ms returns 0
T93BC 001:281.978 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:282.020 - 0.042ms returns 0
T93BC 001:282.058 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:282.097 - 0.040ms returns 0x00000020
T93BC 001:282.134 JLINK_Go()
T93BC 001:282.183   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:286.435 - 4.300ms 
T93BC 001:286.493 JLINK_IsHalted()
T93BC 001:289.960   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:290.756 - 4.262ms returns TRUE
T93BC 001:290.829 JLINK_ReadReg(R15 (PC))
T93BC 001:290.884 - 0.054ms returns 0x20200000
T93BC 001:290.936 JLINK_ClrBPEx(BPHandle = 0x00000020)
T93BC 001:290.993 - 0.057ms returns 0x00
T93BC 001:291.046 JLINK_ReadReg(R0)
T93BC 001:291.095 - 0.048ms returns 0x00000001
T93BC 001:291.147 JLINK_HasError()
T93BC 001:291.199 JLINK_WriteReg(R0, 0x00003C00)
T93BC 001:291.250 - 0.050ms returns 0
T93BC 001:291.301 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:291.349 - 0.048ms returns 0
T93BC 001:291.400 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:291.448 - 0.048ms returns 0
T93BC 001:291.498 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:291.547 - 0.048ms returns 0
T93BC 001:291.597 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:291.644 - 0.047ms returns 0
T93BC 001:291.694 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:291.738 - 0.044ms returns 0
T93BC 001:291.783 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:291.827 - 0.044ms returns 0
T93BC 001:291.874 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:291.930 - 0.056ms returns 0
T93BC 001:291.984 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:292.029 - 0.044ms returns 0
T93BC 001:292.075 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:292.119 - 0.044ms returns 0
T93BC 001:292.166 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:292.211 - 0.044ms returns 0
T93BC 001:292.256 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:292.299 - 0.043ms returns 0
T93BC 001:292.344 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:292.388 - 0.043ms returns 0
T93BC 001:292.433 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:292.506 - 0.072ms returns 0
T93BC 001:292.553 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:292.595 - 0.042ms returns 0
T93BC 001:292.637 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:292.678 - 0.040ms returns 0
T93BC 001:292.721 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:292.762 - 0.040ms returns 0
T93BC 001:292.804 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:292.844 - 0.040ms returns 0
T93BC 001:292.887 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:292.927 - 0.040ms returns 0
T93BC 001:292.970 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:293.011 - 0.041ms returns 0
T93BC 001:293.054 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:293.096 - 0.042ms returns 0x00000021
T93BC 001:293.138 JLINK_Go()
T93BC 001:293.187   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:297.441 - 4.302ms 
T93BC 001:297.498 JLINK_IsHalted()
T93BC 001:298.305 - 0.804ms returns FALSE
T93BC 001:298.415 JLINK_HasError()
T93BC 001:300.496 JLINK_IsHalted()
T93BC 001:304.120   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:304.877 - 4.380ms returns TRUE
T93BC 001:304.953 JLINK_ReadReg(R15 (PC))
T93BC 001:305.011 - 0.057ms returns 0x20200000
T93BC 001:305.067 JLINK_ClrBPEx(BPHandle = 0x00000021)
T93BC 001:305.121 - 0.053ms returns 0x00
T93BC 001:305.176 JLINK_ReadReg(R0)
T93BC 001:305.230 - 0.054ms returns 0x00000000
T93BC 001:307.926 JLINK_HasError()
T93BC 001:308.030 JLINK_WriteReg(R0, 0x00004000)
T93BC 001:308.084 - 0.054ms returns 0
T93BC 001:308.136 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:308.185 - 0.049ms returns 0
T93BC 001:308.239 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:308.297 - 0.058ms returns 0
T93BC 001:308.353 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:308.406 - 0.053ms returns 0
T93BC 001:308.461 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:308.514 - 0.053ms returns 0
T93BC 001:308.569 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:308.619 - 0.050ms returns 0
T93BC 001:308.669 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:308.717 - 0.048ms returns 0
T93BC 001:308.767 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:308.815 - 0.048ms returns 0
T93BC 001:308.865 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:308.912 - 0.047ms returns 0
T93BC 001:308.962 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:309.009 - 0.047ms returns 0
T93BC 001:309.059 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:309.108 - 0.049ms returns 0
T93BC 001:309.163 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:309.240 - 0.077ms returns 0
T93BC 001:309.289 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:309.336 - 0.047ms returns 0
T93BC 001:309.387 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:309.437 - 0.050ms returns 0
T93BC 001:309.487 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:309.537 - 0.050ms returns 0
T93BC 001:309.587 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:309.634 - 0.047ms returns 0
T93BC 001:309.684 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:309.733 - 0.049ms returns 0
T93BC 001:309.783 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:309.831 - 0.048ms returns 0
T93BC 001:309.881 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:309.929 - 0.048ms returns 0
T93BC 001:309.979 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:310.028 - 0.048ms returns 0
T93BC 001:310.080 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:310.132 - 0.052ms returns 0x00000022
T93BC 001:310.178 JLINK_Go()
T93BC 001:310.241   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:314.508 - 4.329ms 
T93BC 001:314.575 JLINK_IsHalted()
T93BC 001:318.545   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:319.319 - 4.743ms returns TRUE
T93BC 001:319.380 JLINK_ReadReg(R15 (PC))
T93BC 001:319.426 - 0.046ms returns 0x20200000
T93BC 001:319.465 JLINK_ClrBPEx(BPHandle = 0x00000022)
T93BC 001:319.501 - 0.037ms returns 0x00
T93BC 001:319.540 JLINK_ReadReg(R0)
T93BC 001:319.576 - 0.036ms returns 0x00000001
T93BC 001:319.614 JLINK_HasError()
T93BC 001:319.653 JLINK_WriteReg(R0, 0x00004000)
T93BC 001:319.690 - 0.037ms returns 0
T93BC 001:319.727 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:319.763 - 0.035ms returns 0
T93BC 001:319.800 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:319.836 - 0.036ms returns 0
T93BC 001:319.872 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:319.907 - 0.035ms returns 0
T93BC 001:319.944 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:319.979 - 0.035ms returns 0
T93BC 001:320.016 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:320.052 - 0.035ms returns 0
T93BC 001:320.088 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:320.124 - 0.035ms returns 0
T93BC 001:320.160 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:320.196 - 0.035ms returns 0
T93BC 001:320.233 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:320.269 - 0.036ms returns 0
T93BC 001:320.306 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:320.341 - 0.035ms returns 0
T93BC 001:320.379 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:320.414 - 0.035ms returns 0
T93BC 001:320.451 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:320.486 - 0.035ms returns 0
T93BC 001:320.523 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:320.558 - 0.035ms returns 0
T93BC 001:320.595 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:320.632 - 0.036ms returns 0
T93BC 001:320.669 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:320.705 - 0.035ms returns 0
T93BC 001:320.742 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:320.777 - 0.035ms returns 0
T93BC 001:320.814 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:320.850 - 0.035ms returns 0
T93BC 001:320.887 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:320.922 - 0.035ms returns 0
T93BC 001:320.959 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:320.994 - 0.035ms returns 0
T93BC 001:321.046 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:321.082 - 0.036ms returns 0
T93BC 001:321.120 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:321.156 - 0.036ms returns 0x00000023
T93BC 001:321.193 JLINK_Go()
T93BC 001:321.239   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:325.262 - 4.068ms 
T93BC 001:325.339 JLINK_IsHalted()
T93BC 001:326.121 - 0.781ms returns FALSE
T93BC 001:326.187 JLINK_HasError()
T93BC 001:327.873 JLINK_IsHalted()
T93BC 001:331.562   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:332.799 - 4.923ms returns TRUE
T93BC 001:332.905 JLINK_ReadReg(R15 (PC))
T93BC 001:332.980 - 0.075ms returns 0x20200000
T93BC 001:333.050 JLINK_ClrBPEx(BPHandle = 0x00000023)
T93BC 001:333.117 - 0.067ms returns 0x00
T93BC 001:333.185 JLINK_ReadReg(R0)
T93BC 001:333.244 - 0.058ms returns 0x00000000
T93BC 001:334.884 JLINK_HasError()
T93BC 001:334.984 JLINK_WriteReg(R0, 0x00004400)
T93BC 001:335.043 - 0.058ms returns 0
T93BC 001:335.098 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:335.150 - 0.052ms returns 0
T93BC 001:335.204 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:335.255 - 0.051ms returns 0
T93BC 001:335.306 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:335.353 - 0.047ms returns 0
T93BC 001:335.404 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:335.452 - 0.048ms returns 0
T93BC 001:335.501 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:335.550 - 0.048ms returns 0
T93BC 001:335.599 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:335.645 - 0.047ms returns 0
T93BC 001:335.694 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:335.742 - 0.047ms returns 0
T93BC 001:335.791 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:335.839 - 0.047ms returns 0
T93BC 001:335.919 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:336.002 - 0.082ms returns 0
T93BC 001:336.051 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:336.099 - 0.047ms returns 0
T93BC 001:336.148 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:336.195 - 0.047ms returns 0
T93BC 001:336.245 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:336.293 - 0.048ms returns 0
T93BC 001:336.342 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:336.404 - 0.061ms returns 0
T93BC 001:336.460 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:336.526 - 0.065ms returns 0
T93BC 001:336.577 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:336.625 - 0.048ms returns 0
T93BC 001:336.675 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:336.723 - 0.048ms returns 0
T93BC 001:336.773 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:336.821 - 0.047ms returns 0
T93BC 001:336.870 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:336.918 - 0.048ms returns 0
T93BC 001:336.968 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:337.016 - 0.048ms returns 0
T93BC 001:337.067 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:337.119 - 0.053ms returns 0x00000024
T93BC 001:337.169 JLINK_Go()
T93BC 001:337.230   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:341.538 - 4.366ms 
T93BC 001:341.647 JLINK_IsHalted()
T93BC 001:345.307   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:346.190 - 4.542ms returns TRUE
T93BC 001:346.261 JLINK_ReadReg(R15 (PC))
T93BC 001:346.327 - 0.065ms returns 0x20200000
T93BC 001:346.386 JLINK_ClrBPEx(BPHandle = 0x00000024)
T93BC 001:346.439 - 0.053ms returns 0x00
T93BC 001:346.493 JLINK_ReadReg(R0)
T93BC 001:346.545 - 0.051ms returns 0x00000001
T93BC 001:346.601 JLINK_HasError()
T93BC 001:346.656 JLINK_WriteReg(R0, 0x00004400)
T93BC 001:346.707 - 0.051ms returns 0
T93BC 001:346.762 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:346.814 - 0.051ms returns 0
T93BC 001:346.869 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:346.920 - 0.051ms returns 0
T93BC 001:346.974 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:347.026 - 0.051ms returns 0
T93BC 001:347.078 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:347.125 - 0.047ms returns 0
T93BC 001:347.175 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:347.223 - 0.048ms returns 0
T93BC 001:347.272 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:347.319 - 0.047ms returns 0
T93BC 001:347.369 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:347.416 - 0.047ms returns 0
T93BC 001:347.466 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:347.513 - 0.047ms returns 0
T93BC 001:347.563 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:347.610 - 0.047ms returns 0
T93BC 001:347.660 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:347.707 - 0.047ms returns 0
T93BC 001:347.756 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:347.803 - 0.047ms returns 0
T93BC 001:347.852 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:347.914 - 0.061ms returns 0
T93BC 001:347.962 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:348.287 - 0.323ms returns 0
T93BC 001:348.343 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:348.508 - 0.163ms returns 0
T93BC 001:348.596 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:348.642 - 0.047ms returns 0
T93BC 001:348.689 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:348.733 - 0.044ms returns 0
T93BC 001:348.778 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:348.821 - 0.043ms returns 0
T93BC 001:348.868 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:348.911 - 0.043ms returns 0
T93BC 001:348.957 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:349.029 - 0.071ms returns 0
T93BC 001:349.079 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:349.123 - 0.045ms returns 0x00000025
T93BC 001:349.165 JLINK_Go()
T93BC 001:349.223   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:353.296 - 4.130ms 
T93BC 001:353.369 JLINK_IsHalted()
T93BC 001:354.231 - 0.860ms returns FALSE
T93BC 001:354.315 JLINK_HasError()
T93BC 001:356.328 JLINK_IsHalted()
T93BC 001:359.907   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:360.728 - 4.399ms returns TRUE
T93BC 001:360.792 JLINK_ReadReg(R15 (PC))
T93BC 001:360.830 - 0.038ms returns 0x20200000
T93BC 001:360.864 JLINK_ClrBPEx(BPHandle = 0x00000025)
T93BC 001:360.897 - 0.033ms returns 0x00
T93BC 001:360.930 JLINK_ReadReg(R0)
T93BC 001:360.962 - 0.031ms returns 0x00000000
T93BC 001:361.720 JLINK_HasError()
T93BC 001:361.772 JLINK_WriteReg(R0, 0x00004800)
T93BC 001:361.805 - 0.033ms returns 0
T93BC 001:361.839 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:361.870 - 0.030ms returns 0
T93BC 001:361.903 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:361.942 - 0.039ms returns 0
T93BC 001:361.980 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:362.012 - 0.032ms returns 0
T93BC 001:362.043 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:362.073 - 0.030ms returns 0
T93BC 001:362.104 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:362.133 - 0.028ms returns 0
T93BC 001:362.161 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:362.188 - 0.027ms returns 0
T93BC 001:362.217 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:362.244 - 0.027ms returns 0
T93BC 001:362.273 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:362.300 - 0.027ms returns 0
T93BC 001:362.329 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:362.356 - 0.027ms returns 0
T93BC 001:362.394 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:362.438 - 0.043ms returns 0
T93BC 001:362.467 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:362.494 - 0.027ms returns 0
T93BC 001:362.525 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:362.554 - 0.029ms returns 0
T93BC 001:362.584 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:362.614 - 0.030ms returns 0
T93BC 001:362.646 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:362.676 - 0.029ms returns 0
T93BC 001:362.707 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:362.736 - 0.030ms returns 0
T93BC 001:362.768 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:362.798 - 0.030ms returns 0
T93BC 001:362.828 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:362.856 - 0.028ms returns 0
T93BC 001:362.885 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:362.913 - 0.028ms returns 0
T93BC 001:362.945 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:362.975 - 0.030ms returns 0
T93BC 001:363.008 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:363.039 - 0.032ms returns 0x00000026
T93BC 001:363.070 JLINK_Go()
T93BC 001:363.113   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:367.195 - 4.123ms 
T93BC 001:367.243 JLINK_IsHalted()
T93BC 001:370.646   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:371.441 - 4.197ms returns TRUE
T93BC 001:371.475 JLINK_ReadReg(R15 (PC))
T93BC 001:371.494 - 0.019ms returns 0x20200000
T93BC 001:371.511 JLINK_ClrBPEx(BPHandle = 0x00000026)
T93BC 001:371.528 - 0.016ms returns 0x00
T93BC 001:371.545 JLINK_ReadReg(R0)
T93BC 001:371.561 - 0.016ms returns 0x00000001
T93BC 001:371.578 JLINK_HasError()
T93BC 001:371.596 JLINK_WriteReg(R0, 0x00004800)
T93BC 001:371.613 - 0.016ms returns 0
T93BC 001:371.629 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:371.645 - 0.015ms returns 0
T93BC 001:371.662 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:371.678 - 0.015ms returns 0
T93BC 001:371.694 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:371.710 - 0.016ms returns 0
T93BC 001:371.727 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:371.742 - 0.015ms returns 0
T93BC 001:371.759 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:371.775 - 0.015ms returns 0
T93BC 001:371.791 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:371.807 - 0.015ms returns 0
T93BC 001:371.823 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:371.839 - 0.015ms returns 0
T93BC 001:371.855 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:371.871 - 0.015ms returns 0
T93BC 001:371.888 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:371.903 - 0.015ms returns 0
T93BC 001:371.920 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:371.935 - 0.015ms returns 0
T93BC 001:371.952 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:371.969 - 0.016ms returns 0
T93BC 001:371.985 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:372.002 - 0.016ms returns 0
T93BC 001:372.018 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:372.035 - 0.016ms returns 0
T93BC 001:372.052 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:372.068 - 0.016ms returns 0
T93BC 001:372.085 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:372.101 - 0.016ms returns 0
T93BC 001:372.119 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:372.135 - 0.016ms returns 0
T93BC 001:372.152 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:372.168 - 0.016ms returns 0
T93BC 001:372.185 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:372.201 - 0.016ms returns 0
T93BC 001:372.218 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:372.235 - 0.017ms returns 0
T93BC 001:372.253 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:372.275 - 0.023ms returns 0x00000027
T93BC 001:372.296 JLINK_Go()
T93BC 001:372.318   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:376.349 - 4.052ms 
T93BC 001:376.375 JLINK_IsHalted()
T93BC 001:376.999 - 0.623ms returns FALSE
T93BC 001:377.022 JLINK_HasError()
T93BC 001:379.466 JLINK_IsHalted()
T93BC 001:382.875   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:383.707 - 4.243ms returns TRUE
T93BC 001:383.769 JLINK_ReadReg(R15 (PC))
T93BC 001:383.805 - 0.035ms returns 0x20200000
T93BC 001:383.840 JLINK_ClrBPEx(BPHandle = 0x00000027)
T93BC 001:383.872 - 0.031ms returns 0x00
T93BC 001:383.907 JLINK_ReadReg(R0)
T93BC 001:383.937 - 0.030ms returns 0x00000000
T93BC 001:384.730 JLINK_HasError()
T93BC 001:384.783 JLINK_WriteReg(R0, 0x00004C00)
T93BC 001:384.814 - 0.031ms returns 0
T93BC 001:384.843 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:384.870 - 0.027ms returns 0
T93BC 001:384.898 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:384.926 - 0.027ms returns 0
T93BC 001:384.954 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:384.981 - 0.027ms returns 0
T93BC 001:385.009 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:385.036 - 0.027ms returns 0
T93BC 001:385.066 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:385.094 - 0.028ms returns 0
T93BC 001:385.124 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:385.153 - 0.028ms returns 0
T93BC 001:385.183 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:385.211 - 0.028ms returns 0
T93BC 001:385.240 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:385.269 - 0.028ms returns 0
T93BC 001:385.298 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:385.326 - 0.028ms returns 0
T93BC 001:385.356 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:385.385 - 0.029ms returns 0
T93BC 001:385.432 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:385.461 - 0.029ms returns 0
T93BC 001:385.491 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:385.520 - 0.028ms returns 0
T93BC 001:385.549 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:385.578 - 0.029ms returns 0
T93BC 001:385.607 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:385.636 - 0.028ms returns 0
T93BC 001:385.666 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:385.694 - 0.028ms returns 0
T93BC 001:385.724 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:385.752 - 0.028ms returns 0
T93BC 001:385.781 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:385.810 - 0.028ms returns 0
T93BC 001:385.840 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:385.868 - 0.028ms returns 0
T93BC 001:385.899 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:385.929 - 0.029ms returns 0
T93BC 001:385.961 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:385.993 - 0.032ms returns 0x00000028
T93BC 001:386.024 JLINK_Go()
T93BC 001:386.066   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:390.228 - 4.202ms 
T93BC 001:390.311 JLINK_IsHalted()
T93BC 001:393.756   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:394.506 - 4.193ms returns TRUE
T93BC 001:394.615 JLINK_ReadReg(R15 (PC))
T93BC 001:394.666 - 0.051ms returns 0x20200000
T93BC 001:394.714 JLINK_ClrBPEx(BPHandle = 0x00000028)
T93BC 001:394.761 - 0.046ms returns 0x00
T93BC 001:394.808 JLINK_ReadReg(R0)
T93BC 001:394.852 - 0.044ms returns 0x00000001
T93BC 001:394.898 JLINK_HasError()
T93BC 001:394.952 JLINK_WriteReg(R0, 0x00004C00)
T93BC 001:394.998 - 0.046ms returns 0
T93BC 001:395.045 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:395.089 - 0.044ms returns 0
T93BC 001:395.135 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:395.179 - 0.044ms returns 0
T93BC 001:395.224 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:395.268 - 0.043ms returns 0
T93BC 001:395.314 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:395.360 - 0.046ms returns 0
T93BC 001:395.406 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:395.544 - 0.138ms returns 0
T93BC 001:395.592 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:395.636 - 0.044ms returns 0
T93BC 001:395.682 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:395.726 - 0.044ms returns 0
T93BC 001:395.773 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:395.817 - 0.044ms returns 0
T93BC 001:395.863 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:395.920 - 0.056ms returns 0
T93BC 001:395.990 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:396.034 - 0.044ms returns 0
T93BC 001:396.084 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:396.131 - 0.048ms returns 0
T93BC 001:396.181 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:396.228 - 0.047ms returns 0
T93BC 001:396.278 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:396.326 - 0.048ms returns 0
T93BC 001:396.376 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:396.424 - 0.048ms returns 0
T93BC 001:396.474 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:396.522 - 0.048ms returns 0
T93BC 001:396.571 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:396.619 - 0.048ms returns 0
T93BC 001:396.669 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:396.716 - 0.047ms returns 0
T93BC 001:396.766 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:396.813 - 0.047ms returns 0
T93BC 001:396.863 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:396.910 - 0.048ms returns 0
T93BC 001:396.983 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:397.036 - 0.054ms returns 0x00000029
T93BC 001:397.085 JLINK_Go()
T93BC 001:397.154   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:401.682 - 4.596ms 
T93BC 001:401.763 JLINK_IsHalted()
T93BC 001:402.453 - 0.690ms returns FALSE
T93BC 001:402.497 JLINK_HasError()
T93BC 001:404.004 JLINK_IsHalted()
T93BC 001:407.433   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:408.259 - 4.254ms returns TRUE
T93BC 001:408.313 JLINK_ReadReg(R15 (PC))
T93BC 001:408.361 - 0.047ms returns 0x20200000
T93BC 001:408.418 JLINK_ClrBPEx(BPHandle = 0x00000029)
T93BC 001:408.473 - 0.055ms returns 0x00
T93BC 001:408.521 JLINK_ReadReg(R0)
T93BC 001:408.565 - 0.044ms returns 0x00000000
T93BC 001:409.670 JLINK_HasError()
T93BC 001:409.750 JLINK_WriteReg(R0, 0x00005000)
T93BC 001:409.804 - 0.053ms returns 0
T93BC 001:409.855 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:409.902 - 0.047ms returns 0
T93BC 001:409.951 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:410.047 - 0.095ms returns 0
T93BC 001:410.098 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:410.148 - 0.050ms returns 0
T93BC 001:410.194 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:410.238 - 0.043ms returns 0
T93BC 001:410.284 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:410.328 - 0.044ms returns 0
T93BC 001:410.374 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:410.417 - 0.043ms returns 0
T93BC 001:410.462 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:410.506 - 0.043ms returns 0
T93BC 001:410.551 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:410.596 - 0.044ms returns 0
T93BC 001:410.641 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:410.685 - 0.044ms returns 0
T93BC 001:410.731 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:410.775 - 0.044ms returns 0
T93BC 001:410.821 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:410.865 - 0.043ms returns 0
T93BC 001:410.910 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:410.954 - 0.044ms returns 0
T93BC 001:411.004 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:411.054 - 0.049ms returns 0
T93BC 001:411.096 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:411.137 - 0.040ms returns 0
T93BC 001:411.180 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:411.220 - 0.040ms returns 0
T93BC 001:411.263 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:411.304 - 0.040ms returns 0
T93BC 001:411.346 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:411.386 - 0.040ms returns 0
T93BC 001:411.429 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:411.470 - 0.041ms returns 0
T93BC 001:411.512 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:411.552 - 0.040ms returns 0
T93BC 001:411.595 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:411.637 - 0.042ms returns 0x0000002A
T93BC 001:411.679 JLINK_Go()
T93BC 001:411.734   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:415.964 - 4.283ms 
T93BC 001:416.048 JLINK_IsHalted()
T93BC 001:419.575   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:420.435 - 4.385ms returns TRUE
T93BC 001:420.489 JLINK_ReadReg(R15 (PC))
T93BC 001:420.539 - 0.049ms returns 0x20200000
T93BC 001:420.588 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T93BC 001:420.633 - 0.045ms returns 0x00
T93BC 001:420.696 JLINK_ReadReg(R0)
T93BC 001:420.747 - 0.051ms returns 0x00000001
T93BC 001:420.795 JLINK_HasError()
T93BC 001:420.842 JLINK_WriteReg(R0, 0x00005000)
T93BC 001:420.887 - 0.045ms returns 0
T93BC 001:420.934 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:420.978 - 0.044ms returns 0
T93BC 001:421.024 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:421.071 - 0.047ms returns 0
T93BC 001:421.120 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:421.167 - 0.047ms returns 0
T93BC 001:421.217 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:421.264 - 0.047ms returns 0
T93BC 001:421.315 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:421.363 - 0.048ms returns 0
T93BC 001:421.412 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:421.459 - 0.047ms returns 0
T93BC 001:421.508 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:421.555 - 0.047ms returns 0
T93BC 001:421.605 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:421.652 - 0.047ms returns 0
T93BC 001:421.701 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:421.749 - 0.047ms returns 0
T93BC 001:421.799 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:421.847 - 0.048ms returns 0
T93BC 001:421.896 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:421.943 - 0.046ms returns 0
T93BC 001:421.991 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:422.038 - 0.046ms returns 0
T93BC 001:422.087 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:422.135 - 0.048ms returns 0
T93BC 001:422.207 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:422.251 - 0.044ms returns 0
T93BC 001:422.296 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:422.340 - 0.044ms returns 0
T93BC 001:422.384 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:422.428 - 0.043ms returns 0
T93BC 001:422.473 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:422.516 - 0.043ms returns 0
T93BC 001:422.562 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:422.606 - 0.044ms returns 0
T93BC 001:422.652 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:422.695 - 0.043ms returns 0
T93BC 001:422.742 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:422.787 - 0.046ms returns 0x0000002B
T93BC 001:422.833 JLINK_Go()
T93BC 001:422.918   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:427.033 - 4.199ms 
T93BC 001:427.103 JLINK_IsHalted()
T93BC 001:427.854 - 0.749ms returns FALSE
T93BC 001:427.909 JLINK_HasError()
T93BC 001:429.373 JLINK_IsHalted()
T93BC 001:433.258   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:434.109 - 4.735ms returns TRUE
T93BC 001:434.213 JLINK_ReadReg(R15 (PC))
T93BC 001:434.268 - 0.055ms returns 0x20200000
T93BC 001:434.320 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T93BC 001:434.370 - 0.050ms returns 0x00
T93BC 001:434.430 JLINK_ReadReg(R0)
T93BC 001:434.476 - 0.046ms returns 0x00000000
T93BC 001:435.723 JLINK_HasError()
T93BC 001:435.830 JLINK_WriteReg(R0, 0x00005400)
T93BC 001:435.880 - 0.050ms returns 0
T93BC 001:435.927 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:435.971 - 0.045ms returns 0
T93BC 001:436.017 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:436.059 - 0.042ms returns 0
T93BC 001:436.101 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:436.142 - 0.041ms returns 0
T93BC 001:436.184 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:436.225 - 0.040ms returns 0
T93BC 001:436.267 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:436.309 - 0.041ms returns 0
T93BC 001:436.351 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:436.392 - 0.041ms returns 0
T93BC 001:436.434 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:436.475 - 0.040ms returns 0
T93BC 001:436.517 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:436.557 - 0.041ms returns 0
T93BC 001:436.600 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:436.640 - 0.040ms returns 0
T93BC 001:436.682 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:436.722 - 0.040ms returns 0
T93BC 001:436.764 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:436.805 - 0.041ms returns 0
T93BC 001:436.848 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:436.885 - 0.037ms returns 0
T93BC 001:436.924 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:436.963 - 0.038ms returns 0
T93BC 001:437.002 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:437.040 - 0.037ms returns 0
T93BC 001:437.079 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:437.126 - 0.047ms returns 0
T93BC 001:437.170 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:437.206 - 0.035ms returns 0
T93BC 001:437.245 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:437.281 - 0.036ms returns 0
T93BC 001:437.318 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:437.354 - 0.036ms returns 0
T93BC 001:437.391 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:437.426 - 0.035ms returns 0
T93BC 001:437.464 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:437.501 - 0.037ms returns 0x0000002C
T93BC 001:437.538 JLINK_Go()
T93BC 001:437.586   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:441.999 - 4.460ms 
T93BC 001:442.070 JLINK_IsHalted()
T93BC 001:445.513   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:446.311 - 4.238ms returns TRUE
T93BC 001:446.434 JLINK_ReadReg(R15 (PC))
T93BC 001:446.497 - 0.062ms returns 0x20200000
T93BC 001:446.607 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T93BC 001:446.662 - 0.055ms returns 0x00
T93BC 001:446.718 JLINK_ReadReg(R0)
T93BC 001:446.771 - 0.053ms returns 0x00000001
T93BC 001:446.827 JLINK_HasError()
T93BC 001:446.884 JLINK_WriteReg(R0, 0x00005400)
T93BC 001:446.938 - 0.054ms returns 0
T93BC 001:446.995 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:447.047 - 0.052ms returns 0
T93BC 001:447.101 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:447.149 - 0.048ms returns 0
T93BC 001:447.199 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:447.246 - 0.047ms returns 0
T93BC 001:447.295 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:447.342 - 0.046ms returns 0
T93BC 001:447.390 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:447.438 - 0.048ms returns 0
T93BC 001:447.488 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:447.553 - 0.064ms returns 0
T93BC 001:447.604 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:447.650 - 0.045ms returns 0
T93BC 001:447.699 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:447.747 - 0.047ms returns 0
T93BC 001:447.797 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:447.844 - 0.047ms returns 0
T93BC 001:448.128 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:448.184 - 0.056ms returns 0
T93BC 001:448.237 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:448.286 - 0.049ms returns 0
T93BC 001:448.336 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:448.385 - 0.048ms returns 0
T93BC 001:448.435 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:448.485 - 0.050ms returns 0
T93BC 001:448.553 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:448.607 - 0.054ms returns 0
T93BC 001:448.662 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:448.714 - 0.053ms returns 0
T93BC 001:448.770 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:448.822 - 0.052ms returns 0
T93BC 001:448.876 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:448.928 - 0.052ms returns 0
T93BC 001:448.983 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:449.034 - 0.051ms returns 0
T93BC 001:449.089 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:449.137 - 0.047ms returns 0
T93BC 001:449.188 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:449.240 - 0.052ms returns 0x0000002D
T93BC 001:449.289 JLINK_Go()
T93BC 001:449.355   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:453.469 - 4.178ms 
T93BC 001:453.552 JLINK_IsHalted()
T93BC 001:454.283 - 0.730ms returns FALSE
T93BC 001:454.345 JLINK_HasError()
T93BC 001:455.609 JLINK_IsHalted()
T93BC 001:459.376   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:460.164 - 4.554ms returns TRUE
T93BC 001:460.234 JLINK_ReadReg(R15 (PC))
T93BC 001:460.283 - 0.049ms returns 0x20200000
T93BC 001:460.334 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T93BC 001:460.379 - 0.044ms returns 0x00
T93BC 001:460.440 JLINK_ReadReg(R0)
T93BC 001:460.484 - 0.043ms returns 0x00000000
T93BC 001:462.447 JLINK_HasError()
T93BC 001:462.531 JLINK_WriteReg(R0, 0x00005800)
T93BC 001:462.572 - 0.041ms returns 0
T93BC 001:462.611 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:462.646 - 0.035ms returns 0
T93BC 001:462.700 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:462.736 - 0.036ms returns 0
T93BC 001:462.773 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:462.809 - 0.035ms returns 0
T93BC 001:462.846 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:462.889 - 0.043ms returns 0
T93BC 001:462.931 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:462.966 - 0.035ms returns 0
T93BC 001:463.003 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:463.039 - 0.035ms returns 0
T93BC 001:463.075 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:463.110 - 0.035ms returns 0
T93BC 001:463.147 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:463.183 - 0.036ms returns 0
T93BC 001:463.217 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:463.251 - 0.033ms returns 0
T93BC 001:463.286 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:463.319 - 0.033ms returns 0
T93BC 001:463.353 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:463.385 - 0.032ms returns 0
T93BC 001:463.420 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:463.453 - 0.032ms returns 0
T93BC 001:463.488 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:463.521 - 0.033ms returns 0
T93BC 001:463.556 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:463.590 - 0.033ms returns 0
T93BC 001:463.625 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:463.658 - 0.033ms returns 0
T93BC 001:463.693 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:463.744 - 0.051ms returns 0
T93BC 001:463.781 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:463.814 - 0.033ms returns 0
T93BC 001:463.849 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:463.882 - 0.033ms returns 0
T93BC 001:463.917 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:463.952 - 0.034ms returns 0
T93BC 001:463.988 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:464.024 - 0.036ms returns 0x0000002E
T93BC 001:464.058 JLINK_Go()
T93BC 001:464.106   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:468.453 - 4.393ms 
T93BC 001:468.545 JLINK_IsHalted()
T93BC 001:472.048   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:472.963 - 4.416ms returns TRUE
T93BC 001:473.028 JLINK_ReadReg(R15 (PC))
T93BC 001:473.092 - 0.064ms returns 0x20200000
T93BC 001:473.148 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T93BC 001:473.201 - 0.053ms returns 0x00
T93BC 001:473.256 JLINK_ReadReg(R0)
T93BC 001:473.310 - 0.054ms returns 0x00000001
T93BC 001:473.366 JLINK_HasError()
T93BC 001:473.422 JLINK_WriteReg(R0, 0x00005800)
T93BC 001:473.471 - 0.049ms returns 0
T93BC 001:473.521 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:473.569 - 0.048ms returns 0
T93BC 001:473.618 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:473.665 - 0.046ms returns 0
T93BC 001:473.714 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:473.761 - 0.047ms returns 0
T93BC 001:473.810 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:473.857 - 0.046ms returns 0
T93BC 001:473.906 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:473.953 - 0.047ms returns 0
T93BC 001:474.004 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:474.051 - 0.047ms returns 0
T93BC 001:474.100 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:474.148 - 0.047ms returns 0
T93BC 001:474.198 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:474.245 - 0.047ms returns 0
T93BC 001:474.294 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:474.342 - 0.047ms returns 0
T93BC 001:474.391 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:474.438 - 0.047ms returns 0
T93BC 001:474.488 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:474.536 - 0.048ms returns 0
T93BC 001:474.586 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:474.633 - 0.047ms returns 0
T93BC 001:474.703 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:474.751 - 0.048ms returns 0
T93BC 001:474.800 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:474.844 - 0.044ms returns 0
T93BC 001:474.890 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:474.933 - 0.043ms returns 0
T93BC 001:474.979 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:475.023 - 0.044ms returns 0
T93BC 001:475.068 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:475.111 - 0.043ms returns 0
T93BC 001:475.157 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:475.201 - 0.044ms returns 0
T93BC 001:475.246 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:475.290 - 0.044ms returns 0
T93BC 001:475.339 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:475.385 - 0.046ms returns 0x0000002F
T93BC 001:475.430 JLINK_Go()
T93BC 001:475.485   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:479.863 - 4.431ms 
T93BC 001:480.066 JLINK_IsHalted()
T93BC 001:480.807 - 0.740ms returns FALSE
T93BC 001:480.862 JLINK_HasError()
T93BC 001:482.303 JLINK_IsHalted()
T93BC 001:486.097   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:486.965 - 4.661ms returns TRUE
T93BC 001:487.081 JLINK_ReadReg(R15 (PC))
T93BC 001:487.145 - 0.063ms returns 0x20200000
T93BC 001:487.204 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T93BC 001:487.255 - 0.050ms returns 0x00
T93BC 001:487.313 JLINK_ReadReg(R0)
T93BC 001:487.364 - 0.050ms returns 0x00000000
T93BC 001:489.471 JLINK_HasError()
T93BC 001:489.578 JLINK_WriteReg(R0, 0x00005C00)
T93BC 001:489.634 - 0.054ms returns 0
T93BC 001:489.684 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:489.734 - 0.049ms returns 0
T93BC 001:489.784 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:489.831 - 0.047ms returns 0
T93BC 001:489.881 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:489.929 - 0.047ms returns 0
T93BC 001:489.978 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:490.026 - 0.047ms returns 0
T93BC 001:490.075 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:490.125 - 0.050ms returns 0
T93BC 001:490.179 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:490.230 - 0.051ms returns 0
T93BC 001:490.308 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:490.362 - 0.053ms returns 0
T93BC 001:490.415 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:490.466 - 0.051ms returns 0
T93BC 001:490.520 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:490.571 - 0.051ms returns 0
T93BC 001:490.624 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:490.676 - 0.052ms returns 0
T93BC 001:490.730 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:490.781 - 0.051ms returns 0
T93BC 001:490.835 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:490.886 - 0.051ms returns 0
T93BC 001:490.940 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:490.987 - 0.048ms returns 0
T93BC 001:491.037 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:491.084 - 0.047ms returns 0
T93BC 001:491.134 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:491.182 - 0.048ms returns 0
T93BC 001:491.231 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:491.454 - 0.223ms returns 0
T93BC 001:491.513 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:491.561 - 0.048ms returns 0
T93BC 001:491.611 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:491.658 - 0.047ms returns 0
T93BC 001:491.707 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:491.754 - 0.047ms returns 0
T93BC 001:491.806 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:491.857 - 0.052ms returns 0x00000030
T93BC 001:491.909 JLINK_Go()
T93BC 001:491.984   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:496.057 - 4.147ms 
T93BC 001:496.106 JLINK_IsHalted()
T93BC 001:499.601   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:500.420 - 4.312ms returns TRUE
T93BC 001:500.472 JLINK_ReadReg(R15 (PC))
T93BC 001:500.518 - 0.045ms returns 0x20200000
T93BC 001:500.562 JLINK_ClrBPEx(BPHandle = 0x00000030)
T93BC 001:500.604 - 0.042ms returns 0x00
T93BC 001:500.648 JLINK_ReadReg(R0)
T93BC 001:500.689 - 0.040ms returns 0x00000001
T93BC 001:500.732 JLINK_HasError()
T93BC 001:500.776 JLINK_WriteReg(R0, 0x00005C00)
T93BC 001:500.819 - 0.042ms returns 0
T93BC 001:500.863 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:500.904 - 0.041ms returns 0
T93BC 001:500.946 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:500.987 - 0.040ms returns 0
T93BC 001:501.030 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:501.071 - 0.041ms returns 0
T93BC 001:501.116 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:501.161 - 0.044ms returns 0
T93BC 001:501.206 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:501.250 - 0.043ms returns 0
T93BC 001:501.295 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:501.339 - 0.043ms returns 0
T93BC 001:501.384 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:501.428 - 0.043ms returns 0
T93BC 001:501.474 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:501.518 - 0.044ms returns 0
T93BC 001:501.563 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:501.609 - 0.045ms returns 0
T93BC 001:501.654 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:501.698 - 0.044ms returns 0
T93BC 001:501.744 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:501.787 - 0.043ms returns 0
T93BC 001:501.848 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:501.890 - 0.042ms returns 0
T93BC 001:501.933 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:501.973 - 0.040ms returns 0
T93BC 001:502.015 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:502.056 - 0.040ms returns 0
T93BC 001:502.099 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:502.156 - 0.056ms returns 0
T93BC 001:502.198 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:502.238 - 0.040ms returns 0
T93BC 001:502.281 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:502.321 - 0.040ms returns 0
T93BC 001:502.364 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:502.404 - 0.040ms returns 0
T93BC 001:502.447 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:502.484 - 0.037ms returns 0
T93BC 001:502.525 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:502.564 - 0.039ms returns 0x00000031
T93BC 001:502.603 JLINK_Go()
T93BC 001:502.652   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:506.852 - 4.247ms 
T93BC 001:506.915 JLINK_IsHalted()
T93BC 001:507.867 - 0.951ms returns FALSE
T93BC 001:507.996 JLINK_HasError()
T93BC 001:511.982 JLINK_IsHalted()
T93BC 001:515.538   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:516.432 - 4.449ms returns TRUE
T93BC 001:516.510 JLINK_ReadReg(R15 (PC))
T93BC 001:516.567 - 0.057ms returns 0x20200000
T93BC 001:516.625 JLINK_ClrBPEx(BPHandle = 0x00000031)
T93BC 001:516.676 - 0.051ms returns 0x00
T93BC 001:516.734 JLINK_ReadReg(R0)
T93BC 001:516.784 - 0.050ms returns 0x00000000
T93BC 001:518.739 JLINK_HasError()
T93BC 001:518.827 JLINK_WriteReg(R0, 0x00006000)
T93BC 001:518.875 - 0.048ms returns 0
T93BC 001:518.919 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:518.960 - 0.041ms returns 0
T93BC 001:519.003 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:519.045 - 0.042ms returns 0
T93BC 001:519.085 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:519.123 - 0.038ms returns 0
T93BC 001:519.163 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:519.201 - 0.038ms returns 0
T93BC 001:519.240 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:519.278 - 0.037ms returns 0
T93BC 001:519.318 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:519.369 - 0.051ms returns 0
T93BC 001:519.410 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:519.447 - 0.038ms returns 0
T93BC 001:519.487 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:519.526 - 0.038ms returns 0
T93BC 001:519.565 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:519.604 - 0.038ms returns 0
T93BC 001:519.644 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:519.682 - 0.037ms returns 0
T93BC 001:519.721 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:519.759 - 0.038ms returns 0
T93BC 001:519.799 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:519.836 - 0.037ms returns 0
T93BC 001:519.877 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:519.937 - 0.060ms returns 0
T93BC 001:519.977 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:520.013 - 0.035ms returns 0
T93BC 001:520.050 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:520.086 - 0.036ms returns 0
T93BC 001:520.123 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:520.159 - 0.036ms returns 0
T93BC 001:520.196 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:520.232 - 0.035ms returns 0
T93BC 001:520.269 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:520.305 - 0.036ms returns 0
T93BC 001:520.342 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:520.378 - 0.036ms returns 0
T93BC 001:520.417 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:520.455 - 0.038ms returns 0x00000032
T93BC 001:520.492 JLINK_Go()
T93BC 001:520.543   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:524.575 - 4.082ms 
T93BC 001:524.646 JLINK_IsHalted()
T93BC 001:528.169   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:528.900 - 4.253ms returns TRUE
T93BC 001:528.963 JLINK_ReadReg(R15 (PC))
T93BC 001:529.020 - 0.057ms returns 0x20200000
T93BC 001:529.076 JLINK_ClrBPEx(BPHandle = 0x00000032)
T93BC 001:529.129 - 0.052ms returns 0x00
T93BC 001:529.183 JLINK_ReadReg(R0)
T93BC 001:529.242 - 0.059ms returns 0x00000001
T93BC 001:529.298 JLINK_HasError()
T93BC 001:529.354 JLINK_WriteReg(R0, 0x00006000)
T93BC 001:529.407 - 0.053ms returns 0
T93BC 001:529.482 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:529.533 - 0.051ms returns 0
T93BC 001:529.588 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:529.639 - 0.051ms returns 0
T93BC 001:529.693 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:529.744 - 0.051ms returns 0
T93BC 001:529.799 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:529.851 - 0.052ms returns 0
T93BC 001:529.905 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:529.955 - 0.050ms returns 0
T93BC 001:530.014 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:530.069 - 0.055ms returns 0
T93BC 001:530.128 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:530.186 - 0.057ms returns 0
T93BC 001:530.245 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:530.301 - 0.056ms returns 0
T93BC 001:530.360 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:530.419 - 0.058ms returns 0
T93BC 001:530.478 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:530.535 - 0.056ms returns 0
T93BC 001:530.594 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:530.677 - 0.082ms returns 0
T93BC 001:530.737 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:530.792 - 0.055ms returns 0
T93BC 001:530.851 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:530.908 - 0.057ms returns 0
T93BC 001:530.967 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:531.023 - 0.056ms returns 0
T93BC 001:531.082 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:531.139 - 0.057ms returns 0
T93BC 001:531.198 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:531.257 - 0.058ms returns 0
T93BC 001:531.317 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:531.729 - 0.409ms returns 0
T93BC 001:531.813 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:531.873 - 0.061ms returns 0
T93BC 001:531.935 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:532.007 - 0.071ms returns 0
T93BC 001:532.071 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:532.128 - 0.058ms returns 0x00000033
T93BC 001:532.184 JLINK_Go()
T93BC 001:532.261   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:536.600 - 4.414ms 
T93BC 001:536.706 JLINK_IsHalted()
T93BC 001:537.431 - 0.724ms returns FALSE
T93BC 001:537.494 JLINK_HasError()
T93BC 001:538.765 JLINK_IsHalted()
T93BC 001:542.579   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:543.454 - 4.687ms returns TRUE
T93BC 001:543.568 JLINK_ReadReg(R15 (PC))
T93BC 001:543.653 - 0.085ms returns 0x20200000
T93BC 001:543.743 JLINK_ClrBPEx(BPHandle = 0x00000033)
T93BC 001:543.820 - 0.077ms returns 0x00
T93BC 001:543.907 JLINK_ReadReg(R0)
T93BC 001:543.981 - 0.074ms returns 0x00000000
T93BC 001:546.039 JLINK_HasError()
T93BC 001:546.157 JLINK_WriteReg(R0, 0x00006400)
T93BC 001:546.228 - 0.071ms returns 0
T93BC 001:546.289 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:546.350 - 0.060ms returns 0
T93BC 001:546.410 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:546.468 - 0.058ms returns 0
T93BC 001:546.529 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:546.587 - 0.057ms returns 0
T93BC 001:546.654 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:546.712 - 0.058ms returns 0
T93BC 001:546.774 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:546.832 - 0.058ms returns 0
T93BC 001:546.893 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:546.951 - 0.058ms returns 0
T93BC 001:547.011 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:547.065 - 0.054ms returns 0
T93BC 001:547.120 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:547.173 - 0.052ms returns 0
T93BC 001:547.254 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:547.308 - 0.053ms returns 0
T93BC 001:547.363 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:547.415 - 0.053ms returns 0
T93BC 001:547.471 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:547.523 - 0.052ms returns 0
T93BC 001:547.578 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:547.632 - 0.053ms returns 0
T93BC 001:547.687 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:547.741 - 0.054ms returns 0
T93BC 001:547.796 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:547.850 - 0.053ms returns 0
T93BC 001:548.144 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:548.212 - 0.069ms returns 0
T93BC 001:548.263 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:548.314 - 0.050ms returns 0
T93BC 001:548.365 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:548.436 - 0.071ms returns 0
T93BC 001:548.496 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:548.617 - 0.119ms returns 0
T93BC 001:548.697 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:548.747 - 0.050ms returns 0
T93BC 001:548.821 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:548.875 - 0.054ms returns 0x00000034
T93BC 001:548.925 JLINK_Go()
T93BC 001:548.994   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:553.477 - 4.550ms 
T93BC 001:553.550 JLINK_IsHalted()
T93BC 001:557.141   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:558.103 - 4.551ms returns TRUE
T93BC 001:558.170 JLINK_ReadReg(R15 (PC))
T93BC 001:558.223 - 0.053ms returns 0x20200000
T93BC 001:558.275 JLINK_ClrBPEx(BPHandle = 0x00000034)
T93BC 001:558.324 - 0.049ms returns 0x00
T93BC 001:558.376 JLINK_ReadReg(R0)
T93BC 001:558.429 - 0.053ms returns 0x00000001
T93BC 001:558.484 JLINK_HasError()
T93BC 001:558.540 JLINK_WriteReg(R0, 0x00006400)
T93BC 001:558.601 - 0.061ms returns 0
T93BC 001:558.655 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:558.706 - 0.051ms returns 0
T93BC 001:558.760 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:558.811 - 0.051ms returns 0
T93BC 001:558.866 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:558.918 - 0.052ms returns 0
T93BC 001:558.968 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:559.015 - 0.046ms returns 0
T93BC 001:559.064 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:559.112 - 0.047ms returns 0
T93BC 001:559.161 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:559.208 - 0.047ms returns 0
T93BC 001:559.258 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:559.305 - 0.047ms returns 0
T93BC 001:559.354 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:559.401 - 0.047ms returns 0
T93BC 001:559.451 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:559.498 - 0.047ms returns 0
T93BC 001:559.548 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:559.595 - 0.047ms returns 0
T93BC 001:559.645 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:559.692 - 0.047ms returns 0
T93BC 001:559.742 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:559.789 - 0.047ms returns 0
T93BC 001:559.859 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:559.908 - 0.048ms returns 0
T93BC 001:559.957 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:560.004 - 0.047ms returns 0
T93BC 001:560.053 JLINK_WriteReg(R15 (PC), 0x20200098)
T93BC 001:560.096 - 0.043ms returns 0
T93BC 001:560.143 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:560.187 - 0.044ms returns 0
T93BC 001:560.233 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:560.277 - 0.044ms returns 0
T93BC 001:560.322 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:560.366 - 0.044ms returns 0
T93BC 001:560.411 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:560.455 - 0.043ms returns 0
T93BC 001:560.501 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:560.546 - 0.045ms returns 0x00000035
T93BC 001:560.591 JLINK_Go()
T93BC 001:560.648   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:564.666 - 4.073ms 
T93BC 001:564.749 JLINK_IsHalted()
T93BC 001:565.533 - 0.783ms returns FALSE
T93BC 001:565.624 JLINK_HasError()
T93BC 001:567.277 JLINK_IsHalted()
T93BC 001:571.130   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:571.969 - 4.692ms returns TRUE
T93BC 001:572.026 JLINK_ReadReg(R15 (PC))
T93BC 001:572.068 - 0.042ms returns 0x20200000
T93BC 001:572.113 JLINK_ClrBPEx(BPHandle = 0x00000035)
T93BC 001:572.151 - 0.038ms returns 0x00
T93BC 001:572.196 JLINK_ReadReg(R0)
T93BC 001:572.233 - 0.037ms returns 0x00000000
T93BC 001:573.213 JLINK_HasError()
T93BC 001:573.277 JLINK_WriteReg(R0, 0x00006800)
T93BC 001:573.316 - 0.040ms returns 0
T93BC 001:573.356 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:573.392 - 0.036ms returns 0
T93BC 001:573.430 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:573.466 - 0.036ms returns 0
T93BC 001:573.503 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:573.539 - 0.036ms returns 0
T93BC 001:573.577 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:573.614 - 0.036ms returns 0
T93BC 001:573.651 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:573.709 - 0.057ms returns 0
T93BC 001:573.748 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:573.793 - 0.046ms returns 0
T93BC 001:573.837 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:573.873 - 0.036ms returns 0
T93BC 001:573.911 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:573.947 - 0.036ms returns 0
T93BC 001:573.985 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:574.021 - 0.036ms returns 0
T93BC 001:574.058 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:574.104 - 0.045ms returns 0
T93BC 001:574.141 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:574.175 - 0.033ms returns 0
T93BC 001:574.210 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:574.243 - 0.033ms returns 0
T93BC 001:574.280 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:574.315 - 0.035ms returns 0
T93BC 001:574.350 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:574.384 - 0.034ms returns 0
T93BC 001:574.420 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:574.454 - 0.034ms returns 0
T93BC 001:574.490 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:574.524 - 0.034ms returns 0
T93BC 001:574.559 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:574.593 - 0.034ms returns 0
T93BC 001:574.649 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:574.684 - 0.034ms returns 0
T93BC 001:574.720 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:574.755 - 0.036ms returns 0
T93BC 001:574.792 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:574.829 - 0.036ms returns 0x00000036
T93BC 001:574.864 JLINK_Go()
T93BC 001:574.911   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:579.160 - 4.293ms 
T93BC 001:579.225 JLINK_IsHalted()
T93BC 001:582.850   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:583.690 - 4.464ms returns TRUE
T93BC 001:583.748 JLINK_ReadReg(R15 (PC))
T93BC 001:583.795 - 0.047ms returns 0x20200000
T93BC 001:583.840 JLINK_ClrBPEx(BPHandle = 0x00000036)
T93BC 001:583.883 - 0.043ms returns 0x00
T93BC 001:583.928 JLINK_ReadReg(R0)
T93BC 001:583.970 - 0.042ms returns 0x00000000
T93BC 001:585.697 JLINK_HasError()
T93BC 001:585.776 JLINK_WriteReg(R0, 0x00006C00)
T93BC 001:585.818 - 0.041ms returns 0
T93BC 001:585.856 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:585.893 - 0.037ms returns 0
T93BC 001:585.932 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:585.969 - 0.036ms returns 0
T93BC 001:586.007 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:586.044 - 0.037ms returns 0
T93BC 001:586.082 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:586.118 - 0.035ms returns 0
T93BC 001:586.156 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:586.193 - 0.036ms returns 0
T93BC 001:586.231 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:586.268 - 0.037ms returns 0
T93BC 001:586.307 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:586.345 - 0.037ms returns 0
T93BC 001:586.383 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:586.420 - 0.037ms returns 0
T93BC 001:586.460 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:586.499 - 0.038ms returns 0
T93BC 001:586.539 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:586.577 - 0.038ms returns 0
T93BC 001:586.617 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:586.655 - 0.038ms returns 0
T93BC 001:586.694 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:586.733 - 0.038ms returns 0
T93BC 001:586.772 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:586.812 - 0.039ms returns 0
T93BC 001:586.851 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:586.889 - 0.038ms returns 0
T93BC 001:586.929 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:586.967 - 0.038ms returns 0
T93BC 001:587.007 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:587.045 - 0.038ms returns 0
T93BC 001:587.084 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:587.145 - 0.060ms returns 0
T93BC 001:587.185 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:587.223 - 0.037ms returns 0
T93BC 001:587.264 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:587.305 - 0.040ms returns 0
T93BC 001:587.349 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:587.392 - 0.044ms returns 0x00000037
T93BC 001:587.435 JLINK_Go()
T93BC 001:587.493   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:591.716 - 4.279ms 
T93BC 001:591.780 JLINK_IsHalted()
T93BC 001:595.220   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:595.945 - 4.164ms returns TRUE
T93BC 001:596.021 JLINK_ReadReg(R15 (PC))
T93BC 001:596.070 - 0.048ms returns 0x20200000
T93BC 001:596.112 JLINK_ClrBPEx(BPHandle = 0x00000037)
T93BC 001:596.147 - 0.035ms returns 0x00
T93BC 001:596.183 JLINK_ReadReg(R0)
T93BC 001:596.217 - 0.033ms returns 0x00000000
T93BC 001:597.046 JLINK_HasError()
T93BC 001:597.103 JLINK_WriteReg(R0, 0x00007000)
T93BC 001:597.140 - 0.036ms returns 0
T93BC 001:597.182 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:597.216 - 0.033ms returns 0
T93BC 001:597.251 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:597.284 - 0.033ms returns 0
T93BC 001:597.319 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:597.353 - 0.033ms returns 0
T93BC 001:597.387 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:597.420 - 0.032ms returns 0
T93BC 001:597.454 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:597.488 - 0.033ms returns 0
T93BC 001:597.523 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:597.556 - 0.033ms returns 0
T93BC 001:597.591 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:597.624 - 0.032ms returns 0
T93BC 001:597.678 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:597.713 - 0.035ms returns 0
T93BC 001:597.748 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:597.782 - 0.033ms returns 0
T93BC 001:597.816 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:597.852 - 0.035ms returns 0
T93BC 001:598.191 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:598.242 - 0.051ms returns 0
T93BC 001:598.276 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:598.306 - 0.030ms returns 0
T93BC 001:598.380 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:598.417 - 0.036ms returns 0
T93BC 001:598.467 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:598.498 - 0.031ms returns 0
T93BC 001:598.531 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:598.561 - 0.031ms returns 0
T93BC 001:598.593 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:598.622 - 0.028ms returns 0
T93BC 001:598.652 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:598.686 - 0.029ms returns 0
T93BC 001:598.716 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:598.745 - 0.029ms returns 0
T93BC 001:598.774 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:598.803 - 0.028ms returns 0
T93BC 001:598.834 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:598.865 - 0.031ms returns 0x00000038
T93BC 001:598.895 JLINK_Go()
T93BC 001:598.936   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:603.153 - 4.256ms 
T93BC 001:603.211 JLINK_IsHalted()
T93BC 001:606.705   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:607.452 - 4.241ms returns TRUE
T93BC 001:607.526 JLINK_ReadReg(R15 (PC))
T93BC 001:607.576 - 0.049ms returns 0x20200000
T93BC 001:607.623 JLINK_ClrBPEx(BPHandle = 0x00000038)
T93BC 001:607.667 - 0.043ms returns 0x00
T93BC 001:607.712 JLINK_ReadReg(R0)
T93BC 001:607.755 - 0.042ms returns 0x00000000
T93BC 001:608.985 JLINK_HasError()
T93BC 001:609.076 JLINK_WriteReg(R0, 0x00007400)
T93BC 001:609.128 - 0.052ms returns 0
T93BC 001:609.223 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:609.270 - 0.047ms returns 0
T93BC 001:609.316 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:609.361 - 0.045ms returns 0
T93BC 001:609.406 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:609.451 - 0.044ms returns 0
T93BC 001:609.496 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:609.540 - 0.043ms returns 0
T93BC 001:609.586 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:609.629 - 0.043ms returns 0
T93BC 001:609.674 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:609.718 - 0.043ms returns 0
T93BC 001:609.764 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:609.807 - 0.044ms returns 0
T93BC 001:609.853 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:609.898 - 0.044ms returns 0
T93BC 001:609.943 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:609.988 - 0.044ms returns 0
T93BC 001:610.034 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:610.078 - 0.044ms returns 0
T93BC 001:610.124 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:610.166 - 0.041ms returns 0
T93BC 001:610.209 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:610.250 - 0.041ms returns 0
T93BC 001:610.293 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:610.336 - 0.043ms returns 0
T93BC 001:610.378 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:610.419 - 0.041ms returns 0
T93BC 001:610.473 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:610.520 - 0.047ms returns 0
T93BC 001:610.562 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:610.603 - 0.041ms returns 0
T93BC 001:610.646 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:610.687 - 0.040ms returns 0
T93BC 001:610.729 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:610.770 - 0.040ms returns 0
T93BC 001:610.813 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:610.853 - 0.040ms returns 0
T93BC 001:610.897 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:610.941 - 0.044ms returns 0x00000039
T93BC 001:610.983 JLINK_Go()
T93BC 001:611.038   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:615.170 - 4.185ms 
T93BC 001:615.260 JLINK_IsHalted()
T93BC 001:618.864   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:619.678 - 4.417ms returns TRUE
T93BC 001:619.725 JLINK_ReadReg(R15 (PC))
T93BC 001:619.768 - 0.042ms returns 0x20200000
T93BC 001:619.808 JLINK_ClrBPEx(BPHandle = 0x00000039)
T93BC 001:619.847 - 0.038ms returns 0x00
T93BC 001:619.886 JLINK_ReadReg(R0)
T93BC 001:619.924 - 0.037ms returns 0x00000000
T93BC 001:620.863 JLINK_HasError()
T93BC 001:620.930 JLINK_WriteReg(R0, 0x00007800)
T93BC 001:620.969 - 0.039ms returns 0
T93BC 001:621.007 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:621.042 - 0.035ms returns 0
T93BC 001:621.078 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:621.113 - 0.035ms returns 0
T93BC 001:621.150 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:621.186 - 0.035ms returns 0
T93BC 001:621.222 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:621.258 - 0.035ms returns 0
T93BC 001:621.294 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:621.330 - 0.035ms returns 0
T93BC 001:621.367 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:621.424 - 0.056ms returns 0
T93BC 001:621.462 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:621.497 - 0.035ms returns 0
T93BC 001:621.534 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:621.570 - 0.035ms returns 0
T93BC 001:621.606 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:621.641 - 0.035ms returns 0
T93BC 001:621.678 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:621.713 - 0.035ms returns 0
T93BC 001:621.750 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:621.786 - 0.035ms returns 0
T93BC 001:621.822 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:621.858 - 0.036ms returns 0
T93BC 001:621.895 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:621.931 - 0.036ms returns 0
T93BC 001:621.968 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:622.004 - 0.035ms returns 0
T93BC 001:622.041 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:622.077 - 0.035ms returns 0
T93BC 001:622.124 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:622.161 - 0.037ms returns 0
T93BC 001:622.197 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:622.232 - 0.035ms returns 0
T93BC 001:622.269 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:622.303 - 0.033ms returns 0
T93BC 001:622.337 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:622.371 - 0.033ms returns 0
T93BC 001:622.407 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:622.442 - 0.035ms returns 0x0000003A
T93BC 001:622.477 JLINK_Go()
T93BC 001:622.522   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:626.629 - 4.151ms 
T93BC 001:626.702 JLINK_IsHalted()
T93BC 001:630.191   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:631.028 - 4.324ms returns TRUE
T93BC 001:631.125 JLINK_ReadReg(R15 (PC))
T93BC 001:631.183 - 0.058ms returns 0x20200000
T93BC 001:631.234 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T93BC 001:631.284 - 0.049ms returns 0x00
T93BC 001:631.491 JLINK_ReadReg(R0)
T93BC 001:631.578 - 0.086ms returns 0x00000000
T93BC 001:633.881 JLINK_HasError()
T93BC 001:633.977 JLINK_WriteReg(R0, 0x00007C00)
T93BC 001:634.028 - 0.050ms returns 0
T93BC 001:634.075 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:634.119 - 0.044ms returns 0
T93BC 001:634.165 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:634.209 - 0.044ms returns 0
T93BC 001:634.256 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:634.299 - 0.043ms returns 0
T93BC 001:634.345 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:634.389 - 0.043ms returns 0
T93BC 001:634.435 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:634.490 - 0.055ms returns 0
T93BC 001:634.541 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:634.585 - 0.043ms returns 0
T93BC 001:634.630 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:634.674 - 0.043ms returns 0
T93BC 001:634.719 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:634.762 - 0.043ms returns 0
T93BC 001:634.807 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:634.851 - 0.044ms returns 0
T93BC 001:634.897 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:634.941 - 0.044ms returns 0
T93BC 001:634.986 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:635.030 - 0.044ms returns 0
T93BC 001:635.076 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:635.145 - 0.069ms returns 0
T93BC 001:635.192 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:635.236 - 0.045ms returns 0
T93BC 001:635.282 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:635.327 - 0.045ms returns 0
T93BC 001:635.377 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:635.424 - 0.047ms returns 0
T93BC 001:635.475 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:635.522 - 0.047ms returns 0
T93BC 001:635.572 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:635.620 - 0.047ms returns 0
T93BC 001:635.669 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:635.717 - 0.047ms returns 0
T93BC 001:635.766 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:635.813 - 0.048ms returns 0
T93BC 001:635.866 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:636.015 - 0.150ms returns 0x0000003B
T93BC 001:636.064 JLINK_Go()
T93BC 001:636.129   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:640.446 - 4.379ms 
T93BC 001:640.547 JLINK_IsHalted()
T93BC 001:644.148   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:644.966 - 4.418ms returns TRUE
T93BC 001:645.044 JLINK_ReadReg(R15 (PC))
T93BC 001:645.114 - 0.070ms returns 0x20200000
T93BC 001:645.184 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T93BC 001:645.249 - 0.065ms returns 0x00
T93BC 001:645.323 JLINK_ReadReg(R0)
T93BC 001:645.387 - 0.063ms returns 0x00000000
T93BC 001:650.546 JLINK_HasError()
T93BC 001:650.641 JLINK_WriteReg(R0, 0x00008000)
T93BC 001:650.696 - 0.054ms returns 0
T93BC 001:650.744 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:650.790 - 0.046ms returns 0
T93BC 001:650.837 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:650.881 - 0.044ms returns 0
T93BC 001:650.926 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:650.971 - 0.044ms returns 0
T93BC 001:651.017 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:651.061 - 0.044ms returns 0
T93BC 001:651.108 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:651.153 - 0.045ms returns 0
T93BC 001:651.198 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:651.239 - 0.041ms returns 0
T93BC 001:651.282 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:651.324 - 0.041ms returns 0
T93BC 001:651.366 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:651.408 - 0.041ms returns 0
T93BC 001:651.451 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:651.492 - 0.041ms returns 0
T93BC 001:651.536 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:651.577 - 0.041ms returns 0
T93BC 001:651.620 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:651.661 - 0.041ms returns 0
T93BC 001:651.703 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:651.745 - 0.041ms returns 0
T93BC 001:651.788 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:651.830 - 0.041ms returns 0
T93BC 001:651.872 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:651.913 - 0.040ms returns 0
T93BC 001:651.955 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:651.996 - 0.040ms returns 0
T93BC 001:652.038 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:652.079 - 0.041ms returns 0
T93BC 001:652.122 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:652.160 - 0.038ms returns 0
T93BC 001:652.200 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:652.239 - 0.038ms returns 0
T93BC 001:652.295 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:652.334 - 0.040ms returns 0
T93BC 001:652.375 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:652.416 - 0.041ms returns 0x0000003C
T93BC 001:652.455 JLINK_Go()
T93BC 001:652.510   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:656.753 - 4.296ms 
T93BC 001:656.819 JLINK_IsHalted()
T93BC 001:660.388   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:661.248 - 4.427ms returns TRUE
T93BC 001:661.340 JLINK_ReadReg(R15 (PC))
T93BC 001:661.410 - 0.071ms returns 0x20200000
T93BC 001:661.493 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T93BC 001:661.561 - 0.067ms returns 0x00
T93BC 001:661.638 JLINK_ReadReg(R0)
T93BC 001:661.750 - 0.112ms returns 0x00000000
T93BC 001:663.365 JLINK_HasError()
T93BC 001:663.475 JLINK_WriteReg(R0, 0x00008400)
T93BC 001:663.540 - 0.064ms returns 0
T93BC 001:663.599 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:663.658 - 0.058ms returns 0
T93BC 001:663.719 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:663.777 - 0.058ms returns 0
T93BC 001:663.837 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:663.894 - 0.057ms returns 0
T93BC 001:663.956 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:664.013 - 0.057ms returns 0
T93BC 001:664.067 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:664.120 - 0.053ms returns 0
T93BC 001:664.176 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:664.228 - 0.053ms returns 0
T93BC 001:664.283 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:664.336 - 0.053ms returns 0
T93BC 001:664.391 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:664.443 - 0.052ms returns 0
T93BC 001:664.498 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:664.549 - 0.051ms returns 0
T93BC 001:664.603 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:664.966 - 0.362ms returns 0
T93BC 001:665.031 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:665.095 - 0.064ms returns 0
T93BC 001:665.147 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:665.299 - 0.150ms returns 0
T93BC 001:665.363 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:665.414 - 0.052ms returns 0
T93BC 001:665.468 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:665.513 - 0.045ms returns 0
T93BC 001:665.561 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:665.612 - 0.051ms returns 0
T93BC 001:665.659 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:665.705 - 0.045ms returns 0
T93BC 001:665.752 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:665.797 - 0.044ms returns 0
T93BC 001:665.843 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:665.888 - 0.044ms returns 0
T93BC 001:665.934 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:665.979 - 0.044ms returns 0
T93BC 001:666.033 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:666.083 - 0.050ms returns 0x0000003D
T93BC 001:666.126 JLINK_Go()
T93BC 001:666.206   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:670.225 - 4.097ms 
T93BC 001:670.306 JLINK_IsHalted()
T93BC 001:673.812   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:674.565 - 4.258ms returns TRUE
T93BC 001:674.628 JLINK_ReadReg(R15 (PC))
T93BC 001:674.690 - 0.062ms returns 0x20200000
T93BC 001:674.752 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T93BC 001:674.811 - 0.059ms returns 0x00
T93BC 001:674.872 JLINK_ReadReg(R0)
T93BC 001:674.930 - 0.058ms returns 0x00000000
T93BC 001:676.345 JLINK_HasError()
T93BC 001:676.439 JLINK_WriteReg(R0, 0x00008800)
T93BC 001:676.497 - 0.058ms returns 0
T93BC 001:676.553 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:676.607 - 0.054ms returns 0
T93BC 001:676.662 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:676.714 - 0.052ms returns 0
T93BC 001:676.768 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:676.820 - 0.051ms returns 0
T93BC 001:676.874 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:676.926 - 0.052ms returns 0
T93BC 001:676.976 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:677.023 - 0.048ms returns 0
T93BC 001:677.073 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:677.121 - 0.047ms returns 0
T93BC 001:677.171 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:677.222 - 0.050ms returns 0
T93BC 001:677.267 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:677.311 - 0.044ms returns 0
T93BC 001:677.444 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:677.521 - 0.076ms returns 0
T93BC 001:677.568 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:677.612 - 0.044ms returns 0
T93BC 001:677.665 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:677.709 - 0.044ms returns 0
T93BC 001:677.755 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:677.799 - 0.043ms returns 0
T93BC 001:677.844 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:677.889 - 0.045ms returns 0
T93BC 001:677.962 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:678.007 - 0.045ms returns 0
T93BC 001:678.053 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:678.097 - 0.044ms returns 0
T93BC 001:678.143 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:678.187 - 0.043ms returns 0
T93BC 001:678.234 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:678.277 - 0.042ms returns 0
T93BC 001:678.321 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:678.361 - 0.040ms returns 0
T93BC 001:678.403 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:678.444 - 0.040ms returns 0
T93BC 001:678.488 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:678.532 - 0.044ms returns 0x0000003E
T93BC 001:678.575 JLINK_Go()
T93BC 001:678.632   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:682.910 - 4.333ms 
T93BC 001:682.984 JLINK_IsHalted()
T93BC 001:686.511   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:687.210 - 4.225ms returns TRUE
T93BC 001:687.257 JLINK_ReadReg(R15 (PC))
T93BC 001:687.294 - 0.036ms returns 0x20200000
T93BC 001:687.395 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T93BC 001:687.469 - 0.074ms returns 0x00
T93BC 001:687.509 JLINK_ReadReg(R0)
T93BC 001:687.544 - 0.035ms returns 0x00000000
T93BC 001:688.524 JLINK_HasError()
T93BC 001:688.590 JLINK_WriteReg(R0, 0x00008C00)
T93BC 001:688.628 - 0.039ms returns 0
T93BC 001:688.665 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:688.699 - 0.034ms returns 0
T93BC 001:688.735 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:688.770 - 0.034ms returns 0
T93BC 001:688.806 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:688.840 - 0.034ms returns 0
T93BC 001:688.876 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:688.910 - 0.034ms returns 0
T93BC 001:688.947 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:688.980 - 0.032ms returns 0
T93BC 001:689.015 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:689.048 - 0.033ms returns 0
T93BC 001:689.082 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:689.116 - 0.033ms returns 0
T93BC 001:689.152 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:689.186 - 0.034ms returns 0
T93BC 001:689.223 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:689.255 - 0.032ms returns 0
T93BC 001:689.287 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:689.320 - 0.032ms returns 0
T93BC 001:689.353 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:689.385 - 0.031ms returns 0
T93BC 001:689.434 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:689.466 - 0.031ms returns 0
T93BC 001:689.499 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:689.533 - 0.034ms returns 0
T93BC 001:689.570 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:689.606 - 0.035ms returns 0
T93BC 001:689.645 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:689.683 - 0.038ms returns 0
T93BC 001:689.720 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:689.755 - 0.035ms returns 0
T93BC 001:689.792 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:689.829 - 0.036ms returns 0
T93BC 001:689.865 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:689.899 - 0.034ms returns 0
T93BC 001:689.935 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:689.969 - 0.034ms returns 0
T93BC 001:690.006 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:690.042 - 0.037ms returns 0x0000003F
T93BC 001:690.079 JLINK_Go()
T93BC 001:690.127   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:694.376 - 4.295ms 
T93BC 001:694.456 JLINK_IsHalted()
T93BC 001:698.290   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:699.315 - 4.857ms returns TRUE
T93BC 001:699.393 JLINK_ReadReg(R15 (PC))
T93BC 001:699.455 - 0.061ms returns 0x20200000
T93BC 001:699.512 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T93BC 001:699.565 - 0.054ms returns 0x00
T93BC 001:699.621 JLINK_ReadReg(R0)
T93BC 001:699.673 - 0.052ms returns 0x00000000
T93BC 001:700.906 JLINK_HasError()
T93BC 001:700.990 JLINK_WriteReg(R0, 0x00009000)
T93BC 001:701.089 - 0.098ms returns 0
T93BC 001:701.140 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:701.188 - 0.048ms returns 0
T93BC 001:701.235 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:701.280 - 0.044ms returns 0
T93BC 001:701.326 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:701.370 - 0.044ms returns 0
T93BC 001:701.416 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:701.478 - 0.061ms returns 0
T93BC 001:701.524 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:701.568 - 0.044ms returns 0
T93BC 001:701.613 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:701.657 - 0.044ms returns 0
T93BC 001:701.702 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:701.746 - 0.044ms returns 0
T93BC 001:701.792 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:701.836 - 0.043ms returns 0
T93BC 001:701.881 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:701.926 - 0.044ms returns 0
T93BC 001:701.973 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:702.020 - 0.047ms returns 0
T93BC 001:702.070 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:702.114 - 0.044ms returns 0
T93BC 001:702.159 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:702.203 - 0.043ms returns 0
T93BC 001:702.249 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:702.297 - 0.048ms returns 0
T93BC 001:702.354 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:702.398 - 0.044ms returns 0
T93BC 001:702.443 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:702.488 - 0.044ms returns 0
T93BC 001:702.533 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:702.577 - 0.043ms returns 0
T93BC 001:702.622 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:702.666 - 0.043ms returns 0
T93BC 001:702.712 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:702.756 - 0.044ms returns 0
T93BC 001:702.801 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:702.845 - 0.043ms returns 0
T93BC 001:702.892 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:702.938 - 0.047ms returns 0x00000040
T93BC 001:702.983 JLINK_Go()
T93BC 001:703.053   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:707.235 - 4.249ms 
T93BC 001:707.340 JLINK_IsHalted()
T93BC 001:711.096   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:711.964 - 4.622ms returns TRUE
T93BC 001:712.025 JLINK_ReadReg(R15 (PC))
T93BC 001:712.079 - 0.054ms returns 0x20200000
T93BC 001:712.130 JLINK_ClrBPEx(BPHandle = 0x00000040)
T93BC 001:712.177 - 0.047ms returns 0x00
T93BC 001:712.224 JLINK_ReadReg(R0)
T93BC 001:712.268 - 0.043ms returns 0x00000000
T93BC 001:713.466 JLINK_HasError()
T93BC 001:713.542 JLINK_WriteReg(R0, 0x00009400)
T93BC 001:713.587 - 0.045ms returns 0
T93BC 001:713.630 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:713.671 - 0.041ms returns 0
T93BC 001:713.714 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:713.754 - 0.041ms returns 0
T93BC 001:713.797 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:713.837 - 0.040ms returns 0
T93BC 001:713.879 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:713.954 - 0.075ms returns 0
T93BC 001:713.996 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:714.037 - 0.040ms returns 0
T93BC 001:714.080 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:714.120 - 0.041ms returns 0
T93BC 001:714.161 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:714.198 - 0.037ms returns 0
T93BC 001:714.237 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:714.275 - 0.038ms returns 0
T93BC 001:714.314 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:714.353 - 0.038ms returns 0
T93BC 001:714.392 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:714.430 - 0.037ms returns 0
T93BC 001:714.469 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:714.508 - 0.039ms returns 0
T93BC 001:714.548 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:714.585 - 0.037ms returns 0
T93BC 001:714.624 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:714.663 - 0.038ms returns 0
T93BC 001:714.702 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:714.739 - 0.036ms returns 0
T93BC 001:714.778 JLINK_WriteReg(R15 (PC), 0x20200020)
T93BC 001:714.936 - 0.156ms returns 0
T93BC 001:714.983 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:715.021 - 0.038ms returns 0
T93BC 001:715.060 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:715.098 - 0.037ms returns 0
T93BC 001:715.153 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:715.191 - 0.038ms returns 0
T93BC 001:715.229 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:715.266 - 0.036ms returns 0
T93BC 001:715.305 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:715.344 - 0.040ms returns 0x00000041
T93BC 001:715.383 JLINK_Go()
T93BC 001:715.432   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:719.553 - 4.169ms 
T93BC 001:719.629 JLINK_IsHalted()
T93BC 001:723.241   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:724.040 - 4.410ms returns TRUE
T93BC 001:724.099 JLINK_ReadReg(R15 (PC))
T93BC 001:724.157 - 0.058ms returns 0x20200000
T93BC 001:724.219 JLINK_ClrBPEx(BPHandle = 0x00000041)
T93BC 001:724.278 - 0.059ms returns 0x00
T93BC 001:724.329 JLINK_ReadReg(R0)
T93BC 001:724.378 - 0.048ms returns 0x00000000
T93BC 001:725.679 JLINK_HasError()
T93BC 001:725.803 JLINK_WriteReg(R0, 0x00000001)
T93BC 001:725.865 - 0.062ms returns 0
T93BC 001:725.920 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:725.972 - 0.052ms returns 0
T93BC 001:726.026 JLINK_WriteReg(R2, 0x000000FF)
T93BC 001:726.217 - 0.190ms returns 0
T93BC 001:726.269 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:726.317 - 0.048ms returns 0
T93BC 001:726.365 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:726.411 - 0.046ms returns 0
T93BC 001:726.460 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:726.508 - 0.048ms returns 0
T93BC 001:726.558 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:726.605 - 0.047ms returns 0
T93BC 001:726.654 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:726.701 - 0.047ms returns 0
T93BC 001:726.751 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:726.801 - 0.049ms returns 0
T93BC 001:726.850 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:726.898 - 0.048ms returns 0
T93BC 001:726.948 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:726.995 - 0.047ms returns 0
T93BC 001:727.040 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:727.114 - 0.073ms returns 0
T93BC 001:727.161 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:727.205 - 0.043ms returns 0
T93BC 001:727.250 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:727.306 - 0.055ms returns 0
T93BC 001:727.353 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:727.397 - 0.043ms returns 0
T93BC 001:727.442 JLINK_WriteReg(R15 (PC), 0x20200094)
T93BC 001:727.486 - 0.044ms returns 0
T93BC 001:727.534 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:727.577 - 0.043ms returns 0
T93BC 001:727.623 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:727.666 - 0.043ms returns 0
T93BC 001:727.710 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:727.754 - 0.043ms returns 0
T93BC 001:727.799 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:727.843 - 0.044ms returns 0
T93BC 001:727.890 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:727.936 - 0.047ms returns 0x00000042
T93BC 001:727.982 JLINK_Go()
T93BC 001:728.045   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:732.398 - 4.414ms 
T93BC 001:732.527 JLINK_IsHalted()
T93BC 001:736.206   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:737.045 - 4.516ms returns TRUE
T93BC 001:737.105 JLINK_ReadReg(R15 (PC))
T93BC 001:737.159 - 0.054ms returns 0x20200000
T93BC 001:737.213 JLINK_ClrBPEx(BPHandle = 0x00000042)
T93BC 001:737.266 - 0.053ms returns 0x00
T93BC 001:737.321 JLINK_ReadReg(R0)
T93BC 001:737.374 - 0.052ms returns 0x00000000
T93BC 001:802.419 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T93BC 001:802.465   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T93BC 001:802.523   CPU_WriteMem(660 bytes @ 0x20200000)
T93BC 001:810.150 - 7.730ms returns 0x294
T93BC 001:810.254 JLINK_HasError()
T93BC 001:810.296 JLINK_WriteReg(R0, 0x00000000)
T93BC 001:810.336 - 0.039ms returns 0
T93BC 001:810.372 JLINK_WriteReg(R1, 0x01F78A40)
T93BC 001:810.406 - 0.034ms returns 0
T93BC 001:810.442 JLINK_WriteReg(R2, 0x00000002)
T93BC 001:810.475 - 0.033ms returns 0
T93BC 001:810.510 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:810.544 - 0.034ms returns 0
T93BC 001:810.578 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:810.612 - 0.033ms returns 0
T93BC 001:810.652 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:810.687 - 0.034ms returns 0
T93BC 001:810.721 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:810.755 - 0.033ms returns 0
T93BC 001:810.790 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:810.824 - 0.033ms returns 0
T93BC 001:810.858 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:810.893 - 0.034ms returns 0
T93BC 001:810.929 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:810.962 - 0.033ms returns 0
T93BC 001:811.009 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:811.048 - 0.039ms returns 0
T93BC 001:811.083 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:811.116 - 0.033ms returns 0
T93BC 001:811.152 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:811.185 - 0.033ms returns 0
T93BC 001:811.220 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:811.256 - 0.036ms returns 0
T93BC 001:811.293 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:811.329 - 0.036ms returns 0
T93BC 001:811.366 JLINK_WriteReg(R15 (PC), 0x20200038)
T93BC 001:811.401 - 0.034ms returns 0
T93BC 001:811.435 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:811.470 - 0.035ms returns 0
T93BC 001:811.504 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:811.537 - 0.032ms returns 0
T93BC 001:811.572 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:811.606 - 0.033ms returns 0
T93BC 001:811.640 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:811.674 - 0.033ms returns 0
T93BC 001:811.711 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:811.754   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:812.506 - 0.795ms returns 0x00000043
T93BC 001:812.551 JLINK_Go()
T93BC 001:812.586   CPU_WriteMem(2 bytes @ 0x20200000)
T93BC 001:813.414   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:817.581 - 5.028ms 
T93BC 001:817.685 JLINK_IsHalted()
T93BC 001:821.153   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:821.954 - 4.268ms returns TRUE
T93BC 001:821.994 JLINK_ReadReg(R15 (PC))
T93BC 001:822.025 - 0.030ms returns 0x20200000
T93BC 001:822.055 JLINK_ClrBPEx(BPHandle = 0x00000043)
T93BC 001:822.082 - 0.026ms returns 0x00
T93BC 001:822.112 JLINK_ReadReg(R0)
T93BC 001:822.138 - 0.026ms returns 0x00000000
T93BC 001:822.614 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:822.659   Data:  D0 4D 20 20 C1 02 00 00 C5 02 00 00 AD 30 00 00 ...
T93BC 001:822.713   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:827.341 - 4.726ms returns 0x16C
T93BC 001:827.396 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:827.424   Data:  CB FE 10 BD 70 29 02 D1 01 F0 0D F8 10 BD 66 29 ...
T93BC 001:827.480   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:835.058 - 7.661ms returns 0x294
T93BC 001:835.105 JLINK_HasError()
T93BC 001:835.124 JLINK_WriteReg(R0, 0x00000000)
T93BC 001:835.145 - 0.020ms returns 0
T93BC 001:835.163 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:835.180 - 0.016ms returns 0
T93BC 001:835.197 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:835.214 - 0.016ms returns 0
T93BC 001:835.231 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:835.248 - 0.016ms returns 0
T93BC 001:835.265 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:835.282 - 0.017ms returns 0
T93BC 001:835.300 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:835.317 - 0.016ms returns 0
T93BC 001:835.334 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:835.351 - 0.016ms returns 0
T93BC 001:835.368 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:835.385 - 0.016ms returns 0
T93BC 001:835.402 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:835.419 - 0.016ms returns 0
T93BC 001:835.437 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:835.453 - 0.016ms returns 0
T93BC 001:835.484 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:835.501 - 0.017ms returns 0
T93BC 001:835.519 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:835.536 - 0.016ms returns 0
T93BC 001:835.554 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:835.571 - 0.017ms returns 0
T93BC 001:835.590 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:835.608 - 0.018ms returns 0
T93BC 001:835.626 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:835.644 - 0.017ms returns 0
T93BC 001:835.662 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:835.680 - 0.017ms returns 0
T93BC 001:835.698 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:835.716 - 0.017ms returns 0
T93BC 001:835.735 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:835.752 - 0.017ms returns 0
T93BC 001:835.772 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:835.791 - 0.019ms returns 0
T93BC 001:835.810 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:835.827 - 0.017ms returns 0
T93BC 001:835.846 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:835.866 - 0.020ms returns 0x00000044
T93BC 001:835.888 JLINK_Go()
T93BC 001:835.917   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:840.103 - 4.213ms 
T93BC 001:840.134 JLINK_IsHalted()
T93BC 001:840.745 - 0.611ms returns FALSE
T93BC 001:840.775 JLINK_HasError()
T93BC 001:848.472 JLINK_IsHalted()
T93BC 001:852.082   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:852.795 - 4.322ms returns TRUE
T93BC 001:852.846 JLINK_ReadReg(R15 (PC))
T93BC 001:852.862 - 0.015ms returns 0x20200000
T93BC 001:852.876 JLINK_ClrBPEx(BPHandle = 0x00000044)
T93BC 001:852.889 - 0.013ms returns 0x00
T93BC 001:852.903 JLINK_ReadReg(R0)
T93BC 001:852.916 - 0.013ms returns 0x00000000
T93BC 001:853.374 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:853.400   Data:  30 BD 00 1B 30 BD 0A 04 01 D0 80 1E 30 BD 09 02 ...
T93BC 001:853.427   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:857.906 - 4.532ms returns 0x16C
T93BC 001:857.936 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:857.950   Data:  E6 40 02 D1 08 3C F4 D1 D0 1A 70 BD 02 78 0B 78 ...
T93BC 001:857.976   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:865.441 - 7.504ms returns 0x294
T93BC 001:865.480 JLINK_HasError()
T93BC 001:865.516 JLINK_WriteReg(R0, 0x00000400)
T93BC 001:865.537 - 0.021ms returns 0
T93BC 001:865.551 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:865.564 - 0.013ms returns 0
T93BC 001:865.578 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:865.591 - 0.013ms returns 0
T93BC 001:865.605 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:865.618 - 0.013ms returns 0
T93BC 001:865.631 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:865.644 - 0.012ms returns 0
T93BC 001:865.657 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:865.670 - 0.012ms returns 0
T93BC 001:865.683 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:865.696 - 0.013ms returns 0
T93BC 001:865.710 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:865.723 - 0.012ms returns 0
T93BC 001:865.737 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:865.750 - 0.013ms returns 0
T93BC 001:865.764 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:865.777 - 0.013ms returns 0
T93BC 001:865.791 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:865.804 - 0.013ms returns 0
T93BC 001:865.818 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:865.831 - 0.013ms returns 0
T93BC 001:865.845 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:865.858 - 0.012ms returns 0
T93BC 001:865.872 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:865.885 - 0.013ms returns 0
T93BC 001:865.898 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:865.911 - 0.013ms returns 0
T93BC 001:865.928 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:865.941 - 0.013ms returns 0
T93BC 001:865.955 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:865.968 - 0.013ms returns 0
T93BC 001:865.982 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:865.994 - 0.013ms returns 0
T93BC 001:866.008 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:866.021 - 0.013ms returns 0
T93BC 001:866.035 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:866.048 - 0.013ms returns 0
T93BC 001:866.062 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:866.076 - 0.014ms returns 0x00000045
T93BC 001:866.090 JLINK_Go()
T93BC 001:866.111   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:870.128 - 4.036ms 
T93BC 001:870.157 JLINK_IsHalted()
T93BC 001:870.752 - 0.595ms returns FALSE
T93BC 001:870.781 JLINK_HasError()
T93BC 001:872.476 JLINK_IsHalted()
T93BC 001:873.129 - 0.653ms returns FALSE
T93BC 001:873.146 JLINK_HasError()
T93BC 001:874.487 JLINK_IsHalted()
T93BC 001:875.210 - 0.722ms returns FALSE
T93BC 001:875.226 JLINK_HasError()
T93BC 001:876.481 JLINK_IsHalted()
T93BC 001:879.843   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:880.672 - 4.190ms returns TRUE
T93BC 001:880.697 JLINK_ReadReg(R15 (PC))
T93BC 001:880.714 - 0.016ms returns 0x20200000
T93BC 001:880.728 JLINK_ClrBPEx(BPHandle = 0x00000045)
T93BC 001:880.742 - 0.013ms returns 0x00
T93BC 001:880.763 JLINK_ReadReg(R0)
T93BC 001:880.776 - 0.013ms returns 0x00000000
T93BC 001:881.408 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:881.432   Data:  05 D0 05 48 30 BD FF 21 C9 05 08 43 30 BD 08 46 ...
T93BC 001:881.501   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:886.112 - 4.702ms returns 0x16C
T93BC 001:886.194 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:886.233   Data:  7E D0 75 05 6D 0D 7C D0 0D 05 AE 46 AC 43 1B 03 ...
T93BC 001:886.302   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:893.762 - 7.569ms returns 0x294
T93BC 001:893.800 JLINK_HasError()
T93BC 001:893.817 JLINK_WriteReg(R0, 0x00000800)
T93BC 001:893.836 - 0.019ms returns 0
T93BC 001:893.852 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:893.866 - 0.014ms returns 0
T93BC 001:893.882 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:893.896 - 0.014ms returns 0
T93BC 001:893.911 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:893.925 - 0.014ms returns 0
T93BC 001:893.940 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:893.954 - 0.014ms returns 0
T93BC 001:893.969 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:893.983 - 0.014ms returns 0
T93BC 001:893.998 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:894.012 - 0.014ms returns 0
T93BC 001:894.027 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:894.042 - 0.015ms returns 0
T93BC 001:894.058 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:894.073 - 0.014ms returns 0
T93BC 001:894.089 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:894.105 - 0.016ms returns 0
T93BC 001:894.121 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:894.136 - 0.014ms returns 0
T93BC 001:894.152 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:894.172 - 0.020ms returns 0
T93BC 001:894.187 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:894.201 - 0.014ms returns 0
T93BC 001:894.216 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:894.230 - 0.014ms returns 0
T93BC 001:894.245 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:894.259 - 0.014ms returns 0
T93BC 001:894.282 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:894.297 - 0.015ms returns 0
T93BC 001:894.312 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:894.326 - 0.014ms returns 0
T93BC 001:894.341 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:894.355 - 0.014ms returns 0
T93BC 001:894.370 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:894.384 - 0.014ms returns 0
T93BC 001:894.399 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:894.413 - 0.014ms returns 0
T93BC 001:894.429 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:894.444 - 0.015ms returns 0x00000046
T93BC 001:894.459 JLINK_Go()
T93BC 001:894.479   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:898.368 - 3.908ms 
T93BC 001:898.414 JLINK_IsHalted()
T93BC 001:899.097 - 0.682ms returns FALSE
T93BC 001:899.137 JLINK_HasError()
T93BC 001:902.598 JLINK_IsHalted()
T93BC 001:903.275 - 0.677ms returns FALSE
T93BC 001:903.291 JLINK_HasError()
T93BC 001:904.596 JLINK_IsHalted()
T93BC 001:907.943   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:908.675 - 4.078ms returns TRUE
T93BC 001:908.697 JLINK_ReadReg(R15 (PC))
T93BC 001:908.714 - 0.017ms returns 0x20200000
T93BC 001:908.730 JLINK_ClrBPEx(BPHandle = 0x00000046)
T93BC 001:908.745 - 0.015ms returns 0x00
T93BC 001:908.761 JLINK_ReadReg(R0)
T93BC 001:908.776 - 0.015ms returns 0x00000000
T93BC 001:909.556 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:909.590   Data:  29 43 2C 43 09 02 20 02 09 0A D2 1A 4B 0C 7F 24 ...
T93BC 001:909.618   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:914.254 - 4.698ms returns 0x16C
T93BC 001:914.275 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:914.292   Data:  C8 17 C0 43 58 40 10 BD 1F 22 03 0C 02 D1 0F 22 ...
T93BC 001:914.320   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:921.759 - 7.483ms returns 0x294
T93BC 001:921.795 JLINK_HasError()
T93BC 001:921.813 JLINK_WriteReg(R0, 0x00000C00)
T93BC 001:921.832 - 0.019ms returns 0
T93BC 001:921.847 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:921.861 - 0.014ms returns 0
T93BC 001:921.876 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:921.895 - 0.018ms returns 0
T93BC 001:921.910 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:921.924 - 0.014ms returns 0
T93BC 001:921.939 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:921.953 - 0.014ms returns 0
T93BC 001:921.968 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:921.985 - 0.017ms returns 0
T93BC 001:922.002 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:922.017 - 0.014ms returns 0
T93BC 001:922.032 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:922.046 - 0.014ms returns 0
T93BC 001:922.061 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:922.075 - 0.014ms returns 0
T93BC 001:922.090 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:922.104 - 0.014ms returns 0
T93BC 001:922.119 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:922.134 - 0.015ms returns 0
T93BC 001:922.149 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:922.163 - 0.014ms returns 0
T93BC 001:922.178 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:922.192 - 0.014ms returns 0
T93BC 001:922.207 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:922.222 - 0.015ms returns 0
T93BC 001:922.237 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:922.252 - 0.014ms returns 0
T93BC 001:922.267 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:922.281 - 0.014ms returns 0
T93BC 001:922.296 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:922.310 - 0.014ms returns 0
T93BC 001:922.325 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:922.340 - 0.014ms returns 0
T93BC 001:922.354 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:922.369 - 0.014ms returns 0
T93BC 001:922.384 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:922.398 - 0.014ms returns 0
T93BC 001:922.413 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:922.429 - 0.015ms returns 0x00000047
T93BC 001:922.444 JLINK_Go()
T93BC 001:922.465   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:926.414 - 3.969ms 
T93BC 001:926.452 JLINK_IsHalted()
T93BC 001:927.208 - 0.755ms returns FALSE
T93BC 001:927.242 JLINK_HasError()
T93BC 001:931.186 JLINK_IsHalted()
T93BC 001:931.830 - 0.642ms returns FALSE
T93BC 001:931.863 JLINK_HasError()
T93BC 001:933.128 JLINK_IsHalted()
T93BC 001:936.466   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:937.160 - 4.027ms returns TRUE
T93BC 001:937.194 JLINK_ReadReg(R15 (PC))
T93BC 001:937.209 - 0.015ms returns 0x20200000
T93BC 001:937.223 JLINK_ClrBPEx(BPHandle = 0x00000047)
T93BC 001:937.237 - 0.013ms returns 0x00
T93BC 001:937.250 JLINK_ReadReg(R0)
T93BC 001:937.264 - 0.013ms returns 0x00000000
T93BC 001:937.709 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:937.734   Data:  05 E0 62 68 02 A8 A1 68 40 5D 90 47 6D 1C 05 98 ...
T93BC 001:937.762   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:942.258 - 4.549ms returns 0x16C
T93BC 001:942.290 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:942.303   Data:  70 B5 04 46 0D 46 21 46 10 68 FF F7 5C FE 00 23 ...
T93BC 001:942.328   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:949.744 - 7.453ms returns 0x294
T93BC 001:949.780 JLINK_HasError()
T93BC 001:949.796 JLINK_WriteReg(R0, 0x00001000)
T93BC 001:949.813 - 0.016ms returns 0
T93BC 001:949.826 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:949.839 - 0.013ms returns 0
T93BC 001:949.853 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:949.866 - 0.012ms returns 0
T93BC 001:949.879 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:949.896 - 0.017ms returns 0
T93BC 001:949.909 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:949.922 - 0.012ms returns 0
T93BC 001:949.935 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:949.948 - 0.012ms returns 0
T93BC 001:949.961 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:949.973 - 0.012ms returns 0
T93BC 001:949.987 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:949.999 - 0.012ms returns 0
T93BC 001:950.013 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:950.025 - 0.012ms returns 0
T93BC 001:950.038 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:950.051 - 0.012ms returns 0
T93BC 001:950.064 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:950.078 - 0.013ms returns 0
T93BC 001:950.091 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:950.104 - 0.012ms returns 0
T93BC 001:950.117 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:950.130 - 0.012ms returns 0
T93BC 001:950.143 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:950.160 - 0.017ms returns 0
T93BC 001:950.174 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:950.186 - 0.012ms returns 0
T93BC 001:950.200 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:950.251 - 0.051ms returns 0
T93BC 001:950.266 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:950.279 - 0.012ms returns 0
T93BC 001:950.292 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:950.305 - 0.012ms returns 0
T93BC 001:950.318 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:950.331 - 0.012ms returns 0
T93BC 001:950.355 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:950.368 - 0.013ms returns 0
T93BC 001:950.386 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:950.404 - 0.018ms returns 0x00000048
T93BC 001:950.420 JLINK_Go()
T93BC 001:950.440   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:954.404 - 3.983ms 
T93BC 001:954.441 JLINK_IsHalted()
T93BC 001:955.131 - 0.689ms returns FALSE
T93BC 001:955.150 JLINK_HasError()
T93BC 001:956.517 JLINK_IsHalted()
T93BC 001:957.165 - 0.647ms returns FALSE
T93BC 001:957.211 JLINK_HasError()
T93BC 001:959.046 JLINK_IsHalted()
T93BC 001:959.712 - 0.666ms returns FALSE
T93BC 001:959.739 JLINK_HasError()
T93BC 001:961.067 JLINK_IsHalted()
T93BC 001:964.535   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:965.216 - 4.148ms returns TRUE
T93BC 001:965.241 JLINK_ReadReg(R15 (PC))
T93BC 001:965.265 - 0.023ms returns 0x20200000
T93BC 001:965.278 JLINK_ClrBPEx(BPHandle = 0x00000048)
T93BC 001:965.292 - 0.013ms returns 0x00
T93BC 001:965.306 JLINK_ReadReg(R0)
T93BC 001:965.319 - 0.013ms returns 0x00000000
T93BC 001:965.752 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:965.773   Data:  76 1C 20 62 04 98 86 42 F3 DB 20 78 C0 06 0A D5 ...
T93BC 001:965.799   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:970.426 - 4.674ms returns 0x16C
T93BC 001:970.450 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:970.463   Data:  00 92 15 46 1E E0 75 1E 05 D4 FF F7 DD FE 30 32 ...
T93BC 001:970.488   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 001:977.898 - 7.447ms returns 0x294
T93BC 001:977.925 JLINK_HasError()
T93BC 001:977.941 JLINK_WriteReg(R0, 0x00001400)
T93BC 001:977.958 - 0.017ms returns 0
T93BC 001:977.982 JLINK_WriteReg(R1, 0x00000400)
T93BC 001:977.996 - 0.013ms returns 0
T93BC 001:978.009 JLINK_WriteReg(R2, 0x20200294)
T93BC 001:978.022 - 0.012ms returns 0
T93BC 001:978.035 JLINK_WriteReg(R3, 0x00000000)
T93BC 001:978.048 - 0.013ms returns 0
T93BC 001:978.062 JLINK_WriteReg(R4, 0x00000000)
T93BC 001:978.080 - 0.018ms returns 0
T93BC 001:978.094 JLINK_WriteReg(R5, 0x00000000)
T93BC 001:978.107 - 0.013ms returns 0
T93BC 001:978.121 JLINK_WriteReg(R6, 0x00000000)
T93BC 001:978.133 - 0.012ms returns 0
T93BC 001:978.147 JLINK_WriteReg(R7, 0x00000000)
T93BC 001:978.160 - 0.013ms returns 0
T93BC 001:978.173 JLINK_WriteReg(R8, 0x00000000)
T93BC 001:978.186 - 0.013ms returns 0
T93BC 001:978.200 JLINK_WriteReg(R9, 0x20200290)
T93BC 001:978.213 - 0.012ms returns 0
T93BC 001:978.226 JLINK_WriteReg(R10, 0x00000000)
T93BC 001:978.240 - 0.013ms returns 0
T93BC 001:978.254 JLINK_WriteReg(R11, 0x00000000)
T93BC 001:978.267 - 0.013ms returns 0
T93BC 001:978.280 JLINK_WriteReg(R12, 0x00000000)
T93BC 001:978.293 - 0.013ms returns 0
T93BC 001:978.308 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 001:978.321 - 0.013ms returns 0
T93BC 001:978.335 JLINK_WriteReg(R14, 0x20200001)
T93BC 001:978.348 - 0.013ms returns 0
T93BC 001:978.362 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 001:978.376 - 0.013ms returns 0
T93BC 001:978.391 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 001:978.404 - 0.013ms returns 0
T93BC 001:978.422 JLINK_WriteReg(MSP, 0x20208000)
T93BC 001:978.435 - 0.013ms returns 0
T93BC 001:978.449 JLINK_WriteReg(PSP, 0x20208000)
T93BC 001:978.462 - 0.013ms returns 0
T93BC 001:978.477 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 001:978.490 - 0.013ms returns 0
T93BC 001:978.504 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 001:978.519 - 0.014ms returns 0x00000049
T93BC 001:978.532 JLINK_Go()
T93BC 001:978.551   CPU_ReadMem(4 bytes @ 0x********)
T93BC 001:982.395 - 3.862ms 
T93BC 001:982.430 JLINK_IsHalted()
T93BC 001:983.126 - 0.695ms returns FALSE
T93BC 001:983.181 JLINK_HasError()
T93BC 001:985.060 JLINK_IsHalted()
T93BC 001:985.811 - 0.750ms returns FALSE
T93BC 001:985.836 JLINK_HasError()
T93BC 001:987.043 JLINK_IsHalted()
T93BC 001:987.882 - 0.837ms returns FALSE
T93BC 001:987.908 JLINK_HasError()
T93BC 001:989.119 JLINK_IsHalted()
T93BC 001:992.560   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 001:993.187 - 4.067ms returns TRUE
T93BC 001:993.204 JLINK_ReadReg(R15 (PC))
T93BC 001:993.219 - 0.015ms returns 0x20200000
T93BC 001:993.233 JLINK_ClrBPEx(BPHandle = 0x00000049)
T93BC 001:993.247 - 0.013ms returns 0x00
T93BC 001:993.261 JLINK_ReadReg(R0)
T93BC 001:993.274 - 0.013ms returns 0x00000000
T93BC 001:993.708 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 001:993.730   Data:  03 A8 62 68 80 5D A1 68 02 E0 62 68 A1 68 30 20 ...
T93BC 001:993.756   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 001:998.289 - 4.580ms returns 0x16C
T93BC 001:998.326 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 001:998.341   Data:  31 0D 81 42 02 D0 01 98 40 1C 01 90 20 88 00 05 ...
T93BC 001:998.379   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:005.834 - 7.506ms returns 0x294
T93BC 002:005.871 JLINK_HasError()
T93BC 002:005.887 JLINK_WriteReg(R0, 0x00001800)
T93BC 002:005.904 - 0.016ms returns 0
T93BC 002:005.917 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:005.930 - 0.013ms returns 0
T93BC 002:005.944 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:005.956 - 0.012ms returns 0
T93BC 002:005.970 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:005.983 - 0.013ms returns 0
T93BC 002:005.996 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:006.009 - 0.012ms returns 0
T93BC 002:006.022 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:006.035 - 0.012ms returns 0
T93BC 002:006.049 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:006.062 - 0.012ms returns 0
T93BC 002:006.075 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:006.088 - 0.013ms returns 0
T93BC 002:006.101 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:006.114 - 0.013ms returns 0
T93BC 002:006.128 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:006.140 - 0.013ms returns 0
T93BC 002:006.154 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:006.168 - 0.013ms returns 0
T93BC 002:006.181 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:006.194 - 0.013ms returns 0
T93BC 002:006.208 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:006.221 - 0.012ms returns 0
T93BC 002:006.234 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:006.247 - 0.013ms returns 0
T93BC 002:006.261 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:006.274 - 0.013ms returns 0
T93BC 002:006.287 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:006.300 - 0.013ms returns 0
T93BC 002:006.314 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:006.327 - 0.012ms returns 0
T93BC 002:006.340 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:006.353 - 0.012ms returns 0
T93BC 002:006.367 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:006.380 - 0.013ms returns 0
T93BC 002:006.393 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:006.406 - 0.013ms returns 0
T93BC 002:006.420 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:006.434 - 0.014ms returns 0x0000004A
T93BC 002:006.448 JLINK_Go()
T93BC 002:006.468   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:010.405 - 3.957ms 
T93BC 002:010.439 JLINK_IsHalted()
T93BC 002:011.049 - 0.609ms returns FALSE
T93BC 002:011.081 JLINK_HasError()
T93BC 002:012.499 JLINK_IsHalted()
T93BC 002:013.121 - 0.621ms returns FALSE
T93BC 002:013.138 JLINK_HasError()
T93BC 002:014.578 JLINK_IsHalted()
T93BC 002:015.234 - 0.655ms returns FALSE
T93BC 002:015.266 JLINK_HasError()
T93BC 002:016.913 JLINK_IsHalted()
T93BC 002:020.226   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:020.902 - 3.988ms returns TRUE
T93BC 002:020.932 JLINK_ReadReg(R15 (PC))
T93BC 002:020.949 - 0.016ms returns 0x20200000
T93BC 002:020.963 JLINK_ClrBPEx(BPHandle = 0x0000004A)
T93BC 002:020.977 - 0.013ms returns 0x00
T93BC 002:020.991 JLINK_ReadReg(R0)
T93BC 002:021.004 - 0.013ms returns 0x00000000
T93BC 002:021.421 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:021.443   Data:  12 18 52 00 89 1A 01 E0 40 1C 0A 39 0A 29 FB D2 ...
T93BC 002:021.470   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:026.021 - 4.598ms returns 0x16C
T93BC 002:026.064 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:026.078   Data:  03 46 7C 33 C0 6F 5A 68 9B 68 0D C4 2E 48 78 44 ...
T93BC 002:026.104   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:033.522 - 7.457ms returns 0x294
T93BC 002:033.562 JLINK_HasError()
T93BC 002:033.582 JLINK_WriteReg(R0, 0x00001C00)
T93BC 002:033.599 - 0.017ms returns 0
T93BC 002:033.612 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:033.626 - 0.013ms returns 0
T93BC 002:033.639 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:033.652 - 0.012ms returns 0
T93BC 002:033.666 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:033.678 - 0.013ms returns 0
T93BC 002:033.692 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:033.705 - 0.012ms returns 0
T93BC 002:033.718 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:033.731 - 0.012ms returns 0
T93BC 002:033.744 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:033.757 - 0.012ms returns 0
T93BC 002:033.771 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:033.784 - 0.013ms returns 0
T93BC 002:033.797 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:033.810 - 0.013ms returns 0
T93BC 002:033.823 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:033.836 - 0.012ms returns 0
T93BC 002:033.850 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:033.863 - 0.013ms returns 0
T93BC 002:033.877 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:033.890 - 0.013ms returns 0
T93BC 002:033.903 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:033.916 - 0.013ms returns 0
T93BC 002:033.930 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:033.943 - 0.013ms returns 0
T93BC 002:033.957 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:033.970 - 0.012ms returns 0
T93BC 002:033.983 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:033.996 - 0.013ms returns 0
T93BC 002:034.010 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:034.023 - 0.013ms returns 0
T93BC 002:034.036 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:034.050 - 0.013ms returns 0
T93BC 002:034.063 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:034.076 - 0.013ms returns 0
T93BC 002:034.089 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:034.102 - 0.012ms returns 0
T93BC 002:034.116 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:034.130 - 0.014ms returns 0x0000004B
T93BC 002:034.144 JLINK_Go()
T93BC 002:034.164   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:038.287 - 4.142ms 
T93BC 002:038.324 JLINK_IsHalted()
T93BC 002:038.977 - 0.652ms returns FALSE
T93BC 002:038.995 JLINK_HasError()
T93BC 002:041.579 JLINK_IsHalted()
T93BC 002:042.254 - 0.675ms returns FALSE
T93BC 002:042.270 JLINK_HasError()
T93BC 002:043.574 JLINK_IsHalted()
T93BC 002:044.201 - 0.626ms returns FALSE
T93BC 002:044.225 JLINK_HasError()
T93BC 002:045.584 JLINK_IsHalted()
T93BC 002:048.947   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:049.625 - 4.043ms returns TRUE
T93BC 002:049.641 JLINK_ReadReg(R15 (PC))
T93BC 002:049.656 - 0.014ms returns 0x20200000
T93BC 002:049.670 JLINK_ClrBPEx(BPHandle = 0x0000004B)
T93BC 002:049.683 - 0.013ms returns 0x00
T93BC 002:049.697 JLINK_ReadReg(R0)
T93BC 002:049.710 - 0.012ms returns 0x00000000
T93BC 002:050.130 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:050.150   Data:  03 D0 13 E0 00 28 07 D1 20 E0 90 42 04 D8 90 42 ...
T93BC 002:050.175   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:054.674 - 4.544ms returns 0x16C
T93BC 002:054.690 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:054.704   Data:  09 1A C5 40 8C 40 25 43 02 9C 8C 40 00 D0 01 24 ...
T93BC 002:054.726   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:062.176 - 7.484ms returns 0x294
T93BC 002:062.243 JLINK_HasError()
T93BC 002:062.272 JLINK_WriteReg(R0, 0x00002000)
T93BC 002:062.303 - 0.031ms returns 0
T93BC 002:062.330 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:062.357 - 0.026ms returns 0
T93BC 002:062.384 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:062.410 - 0.026ms returns 0
T93BC 002:062.436 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:062.462 - 0.026ms returns 0
T93BC 002:062.489 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:062.515 - 0.025ms returns 0
T93BC 002:062.628 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:062.654 - 0.026ms returns 0
T93BC 002:062.686 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:062.711 - 0.025ms returns 0
T93BC 002:062.738 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:062.764 - 0.026ms returns 0
T93BC 002:062.791 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:062.816 - 0.025ms returns 0
T93BC 002:062.843 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:062.870 - 0.026ms returns 0
T93BC 002:062.897 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:062.923 - 0.026ms returns 0
T93BC 002:062.950 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:062.976 - 0.025ms returns 0
T93BC 002:063.003 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:063.029 - 0.026ms returns 0
T93BC 002:063.055 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:063.082 - 0.026ms returns 0
T93BC 002:063.109 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:063.135 - 0.026ms returns 0
T93BC 002:063.162 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:063.188 - 0.025ms returns 0
T93BC 002:063.214 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:063.240 - 0.025ms returns 0
T93BC 002:063.267 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:063.293 - 0.026ms returns 0
T93BC 002:063.320 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:063.346 - 0.025ms returns 0
T93BC 002:063.373 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:063.400 - 0.027ms returns 0
T93BC 002:063.429 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:063.458 - 0.029ms returns 0x0000004C
T93BC 002:063.486 JLINK_Go()
T93BC 002:063.526   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:067.595 - 4.107ms 
T93BC 002:067.662 JLINK_IsHalted()
T93BC 002:068.408 - 0.745ms returns FALSE
T93BC 002:068.474 JLINK_HasError()
T93BC 002:070.560 JLINK_IsHalted()
T93BC 002:071.240 - 0.680ms returns FALSE
T93BC 002:071.278 JLINK_HasError()
T93BC 002:072.501 JLINK_IsHalted()
T93BC 002:073.191 - 0.690ms returns FALSE
T93BC 002:073.227 JLINK_HasError()
T93BC 002:074.567 JLINK_IsHalted()
T93BC 002:078.036   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:078.721 - 4.153ms returns TRUE
T93BC 002:078.757 JLINK_ReadReg(R15 (PC))
T93BC 002:078.790 - 0.033ms returns 0x20200000
T93BC 002:078.822 JLINK_ClrBPEx(BPHandle = 0x0000004C)
T93BC 002:078.853 - 0.031ms returns 0x00
T93BC 002:078.894 JLINK_ReadReg(R0)
T93BC 002:078.924 - 0.030ms returns 0x00000000
T93BC 002:079.732 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:079.780   Data:  10 BD 00 00 00 00 C0 7F 10 B5 0C 46 11 07 09 0F ...
T93BC 002:079.837   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:084.476 - 4.744ms returns 0x16C
T93BC 002:084.535 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:084.568   Data:  02 48 88 50 01 B0 70 47 04 08 00 00 03 00 00 B1 ...
T93BC 002:084.636   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:092.143 - 7.606ms returns 0x294
T93BC 002:092.215 JLINK_HasError()
T93BC 002:092.250 JLINK_WriteReg(R0, 0x00002400)
T93BC 002:092.283 - 0.033ms returns 0
T93BC 002:092.307 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:092.330 - 0.023ms returns 0
T93BC 002:092.354 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:092.377 - 0.023ms returns 0
T93BC 002:092.401 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:092.424 - 0.023ms returns 0
T93BC 002:092.448 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:092.471 - 0.023ms returns 0
T93BC 002:092.506 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:092.536 - 0.030ms returns 0
T93BC 002:092.568 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:092.600 - 0.031ms returns 0
T93BC 002:092.631 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:092.662 - 0.030ms returns 0
T93BC 002:092.693 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:092.723 - 0.030ms returns 0
T93BC 002:092.754 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:092.784 - 0.030ms returns 0
T93BC 002:092.816 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:092.846 - 0.030ms returns 0
T93BC 002:092.878 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:092.908 - 0.030ms returns 0
T93BC 002:092.940 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:092.970 - 0.030ms returns 0
T93BC 002:093.001 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:093.038 - 0.036ms returns 0
T93BC 002:093.073 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:093.104 - 0.031ms returns 0
T93BC 002:093.137 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:093.169 - 0.032ms returns 0
T93BC 002:093.202 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:093.234 - 0.031ms returns 0
T93BC 002:093.267 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:093.298 - 0.031ms returns 0
T93BC 002:093.332 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:093.363 - 0.031ms returns 0
T93BC 002:093.396 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:093.428 - 0.031ms returns 0
T93BC 002:093.462 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:093.507 - 0.045ms returns 0x0000004D
T93BC 002:093.595 JLINK_Go()
T93BC 002:093.642   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:097.649 - 4.054ms 
T93BC 002:097.683 JLINK_IsHalted()
T93BC 002:098.354 - 0.669ms returns FALSE
T93BC 002:098.381 JLINK_HasError()
T93BC 002:099.607 JLINK_IsHalted()
T93BC 002:100.253 - 0.646ms returns FALSE
T93BC 002:100.278 JLINK_HasError()
T93BC 002:101.581 JLINK_IsHalted()
T93BC 002:102.293 - 0.713ms returns FALSE
T93BC 002:102.332 JLINK_HasError()
T93BC 002:103.570 JLINK_IsHalted()
T93BC 002:104.256 - 0.685ms returns FALSE
T93BC 002:104.294 JLINK_HasError()
T93BC 002:105.540 JLINK_IsHalted()
T93BC 002:109.097   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:109.813 - 4.272ms returns TRUE
T93BC 002:109.880 JLINK_ReadReg(R15 (PC))
T93BC 002:109.918 - 0.037ms returns 0x20200000
T93BC 002:109.964 JLINK_ClrBPEx(BPHandle = 0x0000004D)
T93BC 002:109.998 - 0.034ms returns 0x00
T93BC 002:110.035 JLINK_ReadReg(R0)
T93BC 002:110.069 - 0.034ms returns 0x00000000
T93BC 002:111.250 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:111.321   Data:  01 00 00 26 82 B0 01 90 00 91 01 98 02 49 40 58 ...
T93BC 002:111.394   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:116.114 - 4.866ms returns 0x16C
T93BC 002:116.156 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:116.174   Data:  04 08 00 00 03 00 00 B1 82 B0 01 90 00 91 00 9B ...
T93BC 002:116.206   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:123.769 - 7.610ms returns 0x294
T93BC 002:123.874 JLINK_HasError()
T93BC 002:123.923 JLINK_WriteReg(R0, 0x00002800)
T93BC 002:123.967 - 0.044ms returns 0
T93BC 002:124.010 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:124.047 - 0.037ms returns 0
T93BC 002:124.090 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:124.127 - 0.037ms returns 0
T93BC 002:124.169 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:124.212 - 0.042ms returns 0
T93BC 002:124.253 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:124.290 - 0.037ms returns 0
T93BC 002:124.332 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:124.369 - 0.037ms returns 0
T93BC 002:124.411 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:124.449 - 0.037ms returns 0
T93BC 002:124.492 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:124.530 - 0.037ms returns 0
T93BC 002:124.576 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:124.615 - 0.039ms returns 0
T93BC 002:124.658 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:124.697 - 0.039ms returns 0
T93BC 002:124.739 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:124.777 - 0.037ms returns 0
T93BC 002:124.821 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:124.858 - 0.037ms returns 0
T93BC 002:124.900 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:124.938 - 0.038ms returns 0
T93BC 002:124.983 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:125.020 - 0.038ms returns 0
T93BC 002:125.063 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:125.100 - 0.037ms returns 0
T93BC 002:125.144 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:125.181 - 0.037ms returns 0
T93BC 002:125.229 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:125.266 - 0.042ms returns 0
T93BC 002:125.309 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:125.346 - 0.036ms returns 0
T93BC 002:125.386 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:125.421 - 0.035ms returns 0
T93BC 002:125.461 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:125.515 - 0.054ms returns 0
T93BC 002:125.556 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:125.602 - 0.046ms returns 0x0000004E
T93BC 002:125.648 JLINK_Go()
T93BC 002:125.697   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:130.030 - 4.380ms 
T93BC 002:130.119 JLINK_IsHalted()
T93BC 002:130.778 - 0.658ms returns FALSE
T93BC 002:130.843 JLINK_HasError()
T93BC 002:132.442 JLINK_IsHalted()
T93BC 002:133.117 - 0.674ms returns FALSE
T93BC 002:133.162 JLINK_HasError()
T93BC 002:134.454 JLINK_IsHalted()
T93BC 002:135.064 - 0.609ms returns FALSE
T93BC 002:135.086 JLINK_HasError()
T93BC 002:136.410 JLINK_IsHalted()
T93BC 002:139.779   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:140.443 - 4.032ms returns TRUE
T93BC 002:140.470 JLINK_ReadReg(R15 (PC))
T93BC 002:140.487 - 0.017ms returns 0x20200000
T93BC 002:140.502 JLINK_ClrBPEx(BPHandle = 0x0000004E)
T93BC 002:140.517 - 0.014ms returns 0x00
T93BC 002:140.532 JLINK_ReadReg(R0)
T93BC 002:140.546 - 0.014ms returns 0x00000000
T93BC 002:140.984 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:141.007   Data:  70 47 C0 46 04 01 0B 40 04 02 0B 40 81 B0 00 90 ...
T93BC 002:141.034   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:145.554 - 4.570ms returns 0x16C
T93BC 002:145.588 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:145.605   Data:  36 4A 80 18 0A 78 22 2A 58 D8 93 00 01 A4 E3 58 ...
T93BC 002:145.637   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:153.058 - 7.468ms returns 0x294
T93BC 002:153.110 JLINK_HasError()
T93BC 002:153.148 JLINK_WriteReg(R0, 0x00002C00)
T93BC 002:153.176 - 0.028ms returns 0
T93BC 002:153.199 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:153.220 - 0.021ms returns 0
T93BC 002:153.242 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:153.264 - 0.021ms returns 0
T93BC 002:153.286 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:153.307 - 0.021ms returns 0
T93BC 002:153.330 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:153.353 - 0.022ms returns 0
T93BC 002:153.375 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:153.396 - 0.020ms returns 0
T93BC 002:153.417 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:153.438 - 0.020ms returns 0
T93BC 002:153.460 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:153.481 - 0.020ms returns 0
T93BC 002:153.502 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:153.523 - 0.021ms returns 0
T93BC 002:153.545 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:153.565 - 0.020ms returns 0
T93BC 002:153.587 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:153.610 - 0.022ms returns 0
T93BC 002:153.638 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:153.659 - 0.021ms returns 0
T93BC 002:153.681 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:153.701 - 0.020ms returns 0
T93BC 002:153.723 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:153.744 - 0.021ms returns 0
T93BC 002:153.765 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:153.786 - 0.021ms returns 0
T93BC 002:153.808 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:153.829 - 0.021ms returns 0
T93BC 002:153.850 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:153.871 - 0.021ms returns 0
T93BC 002:153.892 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:153.913 - 0.021ms returns 0
T93BC 002:153.936 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:153.957 - 0.020ms returns 0
T93BC 002:153.980 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:154.002 - 0.022ms returns 0
T93BC 002:154.024 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:154.046 - 0.021ms returns 0x0000004F
T93BC 002:154.067 JLINK_Go()
T93BC 002:154.097   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:158.225 - 4.157ms 
T93BC 002:158.275 JLINK_IsHalted()
T93BC 002:158.949 - 0.674ms returns FALSE
T93BC 002:158.970 JLINK_HasError()
T93BC 002:162.375 JLINK_IsHalted()
T93BC 002:163.044 - 0.668ms returns FALSE
T93BC 002:163.102 JLINK_HasError()
T93BC 002:164.476 JLINK_IsHalted()
T93BC 002:168.023   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:168.797 - 4.320ms returns TRUE
T93BC 002:168.816 JLINK_ReadReg(R15 (PC))
T93BC 002:168.843 - 0.026ms returns 0x20200000
T93BC 002:168.860 JLINK_ClrBPEx(BPHandle = 0x0000004F)
T93BC 002:168.876 - 0.016ms returns 0x00
T93BC 002:168.893 JLINK_ReadReg(R0)
T93BC 002:168.909 - 0.015ms returns 0x00000000
T93BC 002:169.604 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:169.654   Data:  01 92 03 98 0B 49 40 18 02 99 0B 4A FF F7 FF FA ...
T93BC 002:169.708   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:174.265 - 4.660ms returns 0x16C
T93BC 002:174.327 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:174.358   Data:  8A 8E 00 00 BD 8E 00 00 AC 8E 00 00 9B 8E 00 00 ...
T93BC 002:174.414   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:181.884 - 7.556ms returns 0x294
T93BC 002:181.918 JLINK_HasError()
T93BC 002:181.954 JLINK_WriteReg(R0, 0x00003000)
T93BC 002:181.993 - 0.039ms returns 0
T93BC 002:182.027 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:182.060 - 0.032ms returns 0
T93BC 002:182.101 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:182.134 - 0.033ms returns 0
T93BC 002:182.168 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:182.200 - 0.032ms returns 0
T93BC 002:182.232 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:182.265 - 0.032ms returns 0
T93BC 002:182.301 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:182.334 - 0.033ms returns 0
T93BC 002:182.368 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:182.400 - 0.031ms returns 0
T93BC 002:182.432 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:182.463 - 0.031ms returns 0
T93BC 002:182.495 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:182.527 - 0.031ms returns 0
T93BC 002:182.561 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:182.592 - 0.031ms returns 0
T93BC 002:182.625 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:182.656 - 0.031ms returns 0
T93BC 002:182.690 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:182.711 - 0.021ms returns 0
T93BC 002:182.724 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:182.737 - 0.013ms returns 0
T93BC 002:182.751 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:182.764 - 0.013ms returns 0
T93BC 002:182.778 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:182.791 - 0.013ms returns 0
T93BC 002:182.804 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:182.820 - 0.015ms returns 0
T93BC 002:182.833 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:182.846 - 0.013ms returns 0
T93BC 002:182.860 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:182.873 - 0.013ms returns 0
T93BC 002:182.886 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:182.899 - 0.012ms returns 0
T93BC 002:182.912 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:182.926 - 0.013ms returns 0
T93BC 002:182.940 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:182.954 - 0.014ms returns 0x00000050
T93BC 002:182.968 JLINK_Go()
T93BC 002:182.988   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:187.102 - 4.132ms 
T93BC 002:187.181 JLINK_IsHalted()
T93BC 002:187.836 - 0.655ms returns FALSE
T93BC 002:187.870 JLINK_HasError()
T93BC 002:190.443 JLINK_IsHalted()
T93BC 002:191.247 - 0.804ms returns FALSE
T93BC 002:191.276 JLINK_HasError()
T93BC 002:192.371 JLINK_IsHalted()
T93BC 002:193.016 - 0.645ms returns FALSE
T93BC 002:193.032 JLINK_HasError()
T93BC 002:194.443 JLINK_IsHalted()
T93BC 002:197.831   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:198.662 - 4.219ms returns TRUE
T93BC 002:198.720 JLINK_ReadReg(R15 (PC))
T93BC 002:198.740 - 0.020ms returns 0x20200000
T93BC 002:198.757 JLINK_ClrBPEx(BPHandle = 0x00000050)
T93BC 002:198.774 - 0.017ms returns 0x00
T93BC 002:198.791 JLINK_ReadReg(R0)
T93BC 002:198.806 - 0.015ms returns 0x00000000
T93BC 002:199.310 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:199.336   Data:  05 FF 00 F0 19 FA 80 BD 10 B5 CA B0 49 AC 04 94 ...
T93BC 002:199.369   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:203.941 - 4.631ms returns 0x16C
T93BC 002:203.967 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:203.982   Data:  95 E0 00 21 09 A8 40 5E 05 A9 09 78 40 18 00 28 ...
T93BC 002:204.009   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:211.394 - 7.426ms returns 0x294
T93BC 002:211.434 JLINK_HasError()
T93BC 002:211.462 JLINK_WriteReg(R0, 0x00003400)
T93BC 002:211.490 - 0.028ms returns 0
T93BC 002:211.515 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:211.537 - 0.022ms returns 0
T93BC 002:211.550 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:211.568 - 0.017ms returns 0
T93BC 002:211.587 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:211.600 - 0.013ms returns 0
T93BC 002:211.613 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:211.626 - 0.012ms returns 0
T93BC 002:211.640 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:211.652 - 0.012ms returns 0
T93BC 002:211.666 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:211.678 - 0.012ms returns 0
T93BC 002:211.692 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:211.705 - 0.013ms returns 0
T93BC 002:211.719 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:211.732 - 0.013ms returns 0
T93BC 002:211.745 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:211.758 - 0.013ms returns 0
T93BC 002:211.772 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:211.785 - 0.012ms returns 0
T93BC 002:211.798 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:211.811 - 0.013ms returns 0
T93BC 002:211.825 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:211.838 - 0.012ms returns 0
T93BC 002:211.852 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:211.865 - 0.013ms returns 0
T93BC 002:211.878 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:211.891 - 0.012ms returns 0
T93BC 002:211.905 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:211.918 - 0.013ms returns 0
T93BC 002:211.931 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:211.944 - 0.013ms returns 0
T93BC 002:211.958 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:211.971 - 0.012ms returns 0
T93BC 002:211.984 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:211.997 - 0.013ms returns 0
T93BC 002:212.011 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:212.024 - 0.012ms returns 0
T93BC 002:212.038 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:212.051 - 0.014ms returns 0x00000051
T93BC 002:212.065 JLINK_Go()
T93BC 002:212.083   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:215.956 - 3.890ms 
T93BC 002:215.990 JLINK_IsHalted()
T93BC 002:216.665 - 0.674ms returns FALSE
T93BC 002:216.684 JLINK_HasError()
T93BC 002:222.006 JLINK_IsHalted()
T93BC 002:225.507   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:226.204 - 4.199ms returns TRUE
T93BC 002:226.247 JLINK_ReadReg(R15 (PC))
T93BC 002:226.279 - 0.031ms returns 0x20200000
T93BC 002:226.310 JLINK_ClrBPEx(BPHandle = 0x00000051)
T93BC 002:226.339 - 0.029ms returns 0x00
T93BC 002:226.370 JLINK_ReadReg(R0)
T93BC 002:226.399 - 0.029ms returns 0x00000000
T93BC 002:227.070 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:227.121   Data:  10 D1 FF E7 0B 98 03 99 40 18 00 22 0A A9 89 5E ...
T93BC 002:227.174   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:231.711 - 4.640ms returns 0x16C
T93BC 002:231.759 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:231.782   Data:  84 FD 07 98 FE F7 B2 FD 06 99 07 98 FE F7 20 FE ...
T93BC 002:231.824   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:239.209 - 7.448ms returns 0x294
T93BC 002:239.263 JLINK_HasError()
T93BC 002:239.286 JLINK_WriteReg(R0, 0x00003800)
T93BC 002:239.310 - 0.024ms returns 0
T93BC 002:239.330 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:239.350 - 0.019ms returns 0
T93BC 002:239.369 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:239.388 - 0.019ms returns 0
T93BC 002:239.408 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:239.427 - 0.018ms returns 0
T93BC 002:239.446 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:239.464 - 0.018ms returns 0
T93BC 002:239.483 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:239.501 - 0.018ms returns 0
T93BC 002:239.521 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:239.548 - 0.027ms returns 0
T93BC 002:239.568 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:239.586 - 0.018ms returns 0
T93BC 002:239.605 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:239.624 - 0.018ms returns 0
T93BC 002:239.643 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:239.662 - 0.018ms returns 0
T93BC 002:239.681 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:239.700 - 0.018ms returns 0
T93BC 002:239.719 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:239.738 - 0.018ms returns 0
T93BC 002:239.757 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:239.775 - 0.018ms returns 0
T93BC 002:239.795 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:239.815 - 0.019ms returns 0
T93BC 002:239.837 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:239.858 - 0.021ms returns 0
T93BC 002:239.878 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:239.897 - 0.019ms returns 0
T93BC 002:239.916 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:239.935 - 0.019ms returns 0
T93BC 002:239.954 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:239.987 - 0.032ms returns 0
T93BC 002:240.007 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:240.026 - 0.018ms returns 0
T93BC 002:240.045 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:240.063 - 0.018ms returns 0
T93BC 002:240.083 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:240.104 - 0.021ms returns 0x00000052
T93BC 002:240.124 JLINK_Go()
T93BC 002:240.151   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:244.055 - 3.930ms 
T93BC 002:244.108 JLINK_IsHalted()
T93BC 002:244.767 - 0.659ms returns FALSE
T93BC 002:244.792 JLINK_HasError()
T93BC 002:245.978 JLINK_IsHalted()
T93BC 002:246.680 - 0.694ms returns FALSE
T93BC 002:246.726 JLINK_HasError()
T93BC 002:247.925 JLINK_IsHalted()
T93BC 002:248.641 - 0.718ms returns FALSE
T93BC 002:248.664 JLINK_HasError()
T93BC 002:249.935 JLINK_IsHalted()
T93BC 002:250.675 - 0.740ms returns FALSE
T93BC 002:250.691 JLINK_HasError()
T93BC 002:251.983 JLINK_IsHalted()
T93BC 002:255.413   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:256.084 - 4.101ms returns TRUE
T93BC 002:256.118 JLINK_ReadReg(R15 (PC))
T93BC 002:256.135 - 0.016ms returns 0x20200000
T93BC 002:256.149 JLINK_ClrBPEx(BPHandle = 0x00000052)
T93BC 002:256.163 - 0.013ms returns 0x00
T93BC 002:256.177 JLINK_ReadReg(R0)
T93BC 002:256.190 - 0.013ms returns 0x00000000
T93BC 002:256.639 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:256.660   Data:  FF E7 F7 E7 80 BD C0 46 00 0B 20 00 80 B5 84 B0 ...
T93BC 002:256.687   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:261.187 - 4.547ms returns 0x16C
T93BC 002:261.213 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:261.228   Data:  2C 22 00 20 88 54 80 BD D4 0A 20 20 80 B5 88 B0 ...
T93BC 002:261.254   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:268.683 - 7.469ms returns 0x294
T93BC 002:268.730 JLINK_HasError()
T93BC 002:268.750 JLINK_WriteReg(R0, 0x00003C00)
T93BC 002:268.770 - 0.020ms returns 0
T93BC 002:268.786 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:268.801 - 0.015ms returns 0
T93BC 002:268.817 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:268.832 - 0.015ms returns 0
T93BC 002:268.848 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:268.863 - 0.015ms returns 0
T93BC 002:268.878 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:268.894 - 0.015ms returns 0
T93BC 002:268.914 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:268.929 - 0.015ms returns 0
T93BC 002:268.945 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:268.960 - 0.015ms returns 0
T93BC 002:268.976 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:268.991 - 0.015ms returns 0
T93BC 002:269.006 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:269.026 - 0.019ms returns 0
T93BC 002:269.046 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:269.061 - 0.015ms returns 0
T93BC 002:269.077 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:269.093 - 0.016ms returns 0
T93BC 002:269.109 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:269.124 - 0.015ms returns 0
T93BC 002:269.146 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:269.162 - 0.016ms returns 0
T93BC 002:269.180 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:269.196 - 0.016ms returns 0
T93BC 002:269.214 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:269.230 - 0.015ms returns 0
T93BC 002:269.248 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:269.263 - 0.015ms returns 0
T93BC 002:269.281 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:269.297 - 0.015ms returns 0
T93BC 002:269.315 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:269.336 - 0.020ms returns 0
T93BC 002:269.354 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:269.370 - 0.015ms returns 0
T93BC 002:269.388 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:269.404 - 0.016ms returns 0
T93BC 002:269.423 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:269.440 - 0.017ms returns 0x00000053
T93BC 002:269.461 JLINK_Go()
T93BC 002:269.488   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:273.357 - 3.895ms 
T93BC 002:273.396 JLINK_IsHalted()
T93BC 002:274.030 - 0.634ms returns FALSE
T93BC 002:274.052 JLINK_HasError()
T93BC 002:276.669 JLINK_IsHalted()
T93BC 002:277.456 - 0.786ms returns FALSE
T93BC 002:277.494 JLINK_HasError()
T93BC 002:278.711 JLINK_IsHalted()
T93BC 002:279.412 - 0.698ms returns FALSE
T93BC 002:279.434 JLINK_HasError()
T93BC 002:280.723 JLINK_IsHalted()
T93BC 002:284.183   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:284.966 - 4.242ms returns TRUE
T93BC 002:285.011 JLINK_ReadReg(R15 (PC))
T93BC 002:285.041 - 0.029ms returns 0x20200000
T93BC 002:285.067 JLINK_ClrBPEx(BPHandle = 0x00000053)
T93BC 002:285.092 - 0.025ms returns 0x00
T93BC 002:285.116 JLINK_ReadReg(R0)
T93BC 002:285.148 - 0.031ms returns 0x00000000
T93BC 002:285.840 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:285.883   Data:  08 78 0F 24 20 40 08 38 03 22 D3 43 18 40 0A 4A ...
T93BC 002:285.931   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:290.556 - 4.716ms returns 0x16C
T93BC 002:290.596 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:290.638   Data:  00 20 08 70 01 A8 00 78 FF 28 2A D0 FF E7 33 48 ...
T93BC 002:290.690   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:298.144 - 7.546ms returns 0x294
T93BC 002:298.224 JLINK_HasError()
T93BC 002:298.266 JLINK_WriteReg(R0, 0x00004000)
T93BC 002:298.310 - 0.044ms returns 0
T93BC 002:298.366 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:298.404 - 0.037ms returns 0
T93BC 002:298.441 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:298.501 - 0.059ms returns 0
T93BC 002:298.544 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:298.580 - 0.035ms returns 0
T93BC 002:298.617 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:298.652 - 0.034ms returns 0
T93BC 002:298.688 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:298.723 - 0.035ms returns 0
T93BC 002:298.760 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:298.796 - 0.035ms returns 0
T93BC 002:298.837 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:298.874 - 0.036ms returns 0
T93BC 002:298.911 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:298.946 - 0.035ms returns 0
T93BC 002:298.984 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:299.018 - 0.034ms returns 0
T93BC 002:299.056 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:299.091 - 0.035ms returns 0
T93BC 002:299.129 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:299.164 - 0.035ms returns 0
T93BC 002:299.325 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:299.407 - 0.083ms returns 0
T93BC 002:299.448 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:299.484 - 0.035ms returns 0
T93BC 002:299.537 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:299.572 - 0.035ms returns 0
T93BC 002:299.607 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:299.641 - 0.034ms returns 0
T93BC 002:299.676 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:299.709 - 0.033ms returns 0
T93BC 002:299.744 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:299.778 - 0.033ms returns 0
T93BC 002:299.812 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:299.846 - 0.033ms returns 0
T93BC 002:299.881 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:299.915 - 0.034ms returns 0
T93BC 002:299.952 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:299.990 - 0.038ms returns 0x00000054
T93BC 002:300.026 JLINK_Go()
T93BC 002:300.075   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:304.294 - 4.267ms 
T93BC 002:304.361 JLINK_IsHalted()
T93BC 002:305.038 - 0.677ms returns FALSE
T93BC 002:305.078 JLINK_HasError()
T93BC 002:306.204 JLINK_IsHalted()
T93BC 002:306.926 - 0.720ms returns FALSE
T93BC 002:307.004 JLINK_HasError()
T93BC 002:308.244 JLINK_IsHalted()
T93BC 002:308.934 - 0.690ms returns FALSE
T93BC 002:308.969 JLINK_HasError()
T93BC 002:310.191 JLINK_IsHalted()
T93BC 002:310.852 - 0.660ms returns FALSE
T93BC 002:310.909 JLINK_HasError()
T93BC 002:312.210 JLINK_IsHalted()
T93BC 002:315.680   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:316.407 - 4.196ms returns TRUE
T93BC 002:316.446 JLINK_ReadReg(R15 (PC))
T93BC 002:316.479 - 0.033ms returns 0x20200000
T93BC 002:316.516 JLINK_ClrBPEx(BPHandle = 0x00000054)
T93BC 002:316.551 - 0.034ms returns 0x00
T93BC 002:316.582 JLINK_ReadReg(R0)
T93BC 002:316.611 - 0.029ms returns 0x00000000
T93BC 002:317.389 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:317.436   Data:  6A 46 11 70 00 78 05 28 1F D1 FF E7 68 46 00 78 ...
T93BC 002:317.491   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:322.107 - 4.718ms returns 0x16C
T93BC 002:322.150 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:322.172   Data:  13 99 08 60 1A E0 7B 48 00 88 FC F7 21 FC 7A 49 ...
T93BC 002:322.213   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:329.672 - 7.521ms returns 0x294
T93BC 002:329.721 JLINK_HasError()
T93BC 002:329.749 JLINK_WriteReg(R0, 0x00004400)
T93BC 002:329.773 - 0.024ms returns 0
T93BC 002:329.797 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:329.817 - 0.020ms returns 0
T93BC 002:329.841 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:329.861 - 0.020ms returns 0
T93BC 002:329.884 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:329.905 - 0.020ms returns 0
T93BC 002:329.928 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:329.948 - 0.020ms returns 0
T93BC 002:329.971 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:329.991 - 0.020ms returns 0
T93BC 002:330.015 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:330.035 - 0.020ms returns 0
T93BC 002:330.058 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:330.078 - 0.020ms returns 0
T93BC 002:330.102 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:330.122 - 0.020ms returns 0
T93BC 002:330.145 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:330.165 - 0.020ms returns 0
T93BC 002:330.188 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:330.209 - 0.020ms returns 0
T93BC 002:330.232 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:330.253 - 0.020ms returns 0
T93BC 002:330.276 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:330.297 - 0.021ms returns 0
T93BC 002:330.322 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:330.344 - 0.022ms returns 0
T93BC 002:330.368 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:330.389 - 0.021ms returns 0
T93BC 002:330.413 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:330.434 - 0.021ms returns 0
T93BC 002:330.459 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:330.480 - 0.021ms returns 0
T93BC 002:330.504 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:330.525 - 0.021ms returns 0
T93BC 002:330.549 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:330.570 - 0.021ms returns 0
T93BC 002:330.591 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:330.612 - 0.020ms returns 0
T93BC 002:330.634 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:330.656 - 0.022ms returns 0x00000055
T93BC 002:330.679 JLINK_Go()
T93BC 002:330.709   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:334.862 - 4.182ms 
T93BC 002:334.917 JLINK_IsHalted()
T93BC 002:335.632 - 0.714ms returns FALSE
T93BC 002:335.672 JLINK_HasError()
T93BC 002:337.548 JLINK_IsHalted()
T93BC 002:338.400 - 0.851ms returns FALSE
T93BC 002:338.446 JLINK_HasError()
T93BC 002:340.026 JLINK_IsHalted()
T93BC 002:340.769 - 0.742ms returns FALSE
T93BC 002:340.794 JLINK_HasError()
T93BC 002:342.065 JLINK_IsHalted()
T93BC 002:345.469   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:346.288 - 4.221ms returns TRUE
T93BC 002:346.339 JLINK_ReadReg(R15 (PC))
T93BC 002:346.368 - 0.029ms returns 0x20200000
T93BC 002:346.395 JLINK_ClrBPEx(BPHandle = 0x00000055)
T93BC 002:346.421 - 0.025ms returns 0x00
T93BC 002:346.447 JLINK_ReadReg(R0)
T93BC 002:346.472 - 0.025ms returns 0x00000000
T93BC 002:347.376 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:347.411   Data:  03 A8 00 78 04 B0 80 BD 09 0B 20 20 C1 44 00 00 ...
T93BC 002:347.450   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:352.056 - 4.679ms returns 0x16C
T93BC 002:352.100 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:352.122   Data:  A1 FE FE F7 63 FF 02 F0 A5 FF 03 90 FF E7 2D 48 ...
T93BC 002:352.160   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:359.615 - 7.514ms returns 0x294
T93BC 002:359.662 JLINK_HasError()
T93BC 002:359.697 JLINK_WriteReg(R0, 0x00004800)
T93BC 002:359.741 - 0.043ms returns 0
T93BC 002:359.778 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:359.810 - 0.031ms returns 0
T93BC 002:359.844 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:359.883 - 0.038ms returns 0
T93BC 002:359.917 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:359.950 - 0.033ms returns 0
T93BC 002:359.985 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:360.018 - 0.033ms returns 0
T93BC 002:360.053 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:360.086 - 0.033ms returns 0
T93BC 002:360.120 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:360.154 - 0.033ms returns 0
T93BC 002:360.188 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:360.221 - 0.033ms returns 0
T93BC 002:360.256 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:360.289 - 0.033ms returns 0
T93BC 002:360.324 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:360.357 - 0.033ms returns 0
T93BC 002:360.391 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:360.425 - 0.033ms returns 0
T93BC 002:360.460 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:360.493 - 0.033ms returns 0
T93BC 002:360.528 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:360.561 - 0.033ms returns 0
T93BC 002:360.596 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:360.629 - 0.034ms returns 0
T93BC 002:360.664 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:360.696 - 0.032ms returns 0
T93BC 002:360.731 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:360.763 - 0.032ms returns 0
T93BC 002:360.798 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:360.831 - 0.033ms returns 0
T93BC 002:360.864 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:360.898 - 0.033ms returns 0
T93BC 002:360.932 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:360.965 - 0.033ms returns 0
T93BC 002:361.000 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:361.033 - 0.033ms returns 0
T93BC 002:361.068 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:361.101 - 0.033ms returns 0x00000056
T93BC 002:361.134 JLINK_Go()
T93BC 002:361.175   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:365.438 - 4.303ms 
T93BC 002:365.506 JLINK_IsHalted()
T93BC 002:366.290 - 0.783ms returns FALSE
T93BC 002:366.326 JLINK_HasError()
T93BC 002:368.468 JLINK_IsHalted()
T93BC 002:369.147 - 0.679ms returns FALSE
T93BC 002:369.170 JLINK_HasError()
T93BC 002:370.485 JLINK_IsHalted()
T93BC 002:371.223 - 0.738ms returns FALSE
T93BC 002:371.298 JLINK_HasError()
T93BC 002:372.478 JLINK_IsHalted()
T93BC 002:375.992   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:376.752 - 4.272ms returns TRUE
T93BC 002:376.820 JLINK_ReadReg(R15 (PC))
T93BC 002:376.861 - 0.040ms returns 0x20200000
T93BC 002:376.898 JLINK_ClrBPEx(BPHandle = 0x00000056)
T93BC 002:376.938 - 0.040ms returns 0x00
T93BC 002:376.974 JLINK_ReadReg(R0)
T93BC 002:377.008 - 0.033ms returns 0x00000000
T93BC 002:377.476 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:377.498   Data:  01 BC 02 B0 86 46 70 47 68 8F 00 00 FF 03 00 00 ...
T93BC 002:377.524   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:382.033 - 4.556ms returns 0x16C
T93BC 002:382.103 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:382.136   Data:  02 F0 60 FA 04 B0 80 BD 81 B0 00 90 08 49 08 68 ...
T93BC 002:382.198   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:389.767 - 7.662ms returns 0x294
T93BC 002:389.840 JLINK_HasError()
T93BC 002:389.880 JLINK_WriteReg(R0, 0x00004C00)
T93BC 002:389.921 - 0.041ms returns 0
T93BC 002:389.955 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:389.987 - 0.032ms returns 0
T93BC 002:390.020 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:390.052 - 0.031ms returns 0
T93BC 002:390.085 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:390.118 - 0.032ms returns 0
T93BC 002:390.151 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:390.183 - 0.032ms returns 0
T93BC 002:390.216 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:390.248 - 0.032ms returns 0
T93BC 002:390.283 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:390.317 - 0.033ms returns 0
T93BC 002:390.352 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:390.385 - 0.033ms returns 0
T93BC 002:390.420 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:390.454 - 0.034ms returns 0
T93BC 002:390.490 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:390.530 - 0.040ms returns 0
T93BC 002:390.570 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:390.604 - 0.034ms returns 0
T93BC 002:390.638 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:390.672 - 0.033ms returns 0
T93BC 002:390.708 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:390.747 - 0.039ms returns 0
T93BC 002:390.783 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:390.818 - 0.035ms returns 0
T93BC 002:390.854 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:390.887 - 0.033ms returns 0
T93BC 002:390.990 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:391.027 - 0.037ms returns 0
T93BC 002:391.062 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:391.096 - 0.033ms returns 0
T93BC 002:391.131 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:391.164 - 0.033ms returns 0
T93BC 002:391.200 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:391.236 - 0.036ms returns 0
T93BC 002:391.275 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:391.309 - 0.034ms returns 0
T93BC 002:391.346 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:391.383 - 0.037ms returns 0x00000057
T93BC 002:391.419 JLINK_Go()
T93BC 002:391.468   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:395.433 - 4.013ms 
T93BC 002:395.484 JLINK_IsHalted()
T93BC 002:396.347 - 0.855ms returns FALSE
T93BC 002:396.376 JLINK_HasError()
T93BC 002:397.907 JLINK_IsHalted()
T93BC 002:398.643 - 0.735ms returns FALSE
T93BC 002:398.712 JLINK_HasError()
T93BC 002:399.959 JLINK_IsHalted()
T93BC 002:400.676 - 0.716ms returns FALSE
T93BC 002:400.710 JLINK_HasError()
T93BC 002:401.953 JLINK_IsHalted()
T93BC 002:405.510   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:406.291 - 4.337ms returns TRUE
T93BC 002:406.332 JLINK_ReadReg(R15 (PC))
T93BC 002:406.369 - 0.037ms returns 0x20200000
T93BC 002:406.405 JLINK_ClrBPEx(BPHandle = 0x00000057)
T93BC 002:406.440 - 0.034ms returns 0x00
T93BC 002:406.476 JLINK_ReadReg(R0)
T93BC 002:406.510 - 0.034ms returns 0x00000000
T93BC 002:407.433 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:407.535   Data:  88 52 02 B0 70 47 C0 46 B6 0A 20 20 10 B5 8E B0 ...
T93BC 002:407.606   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:412.187 - 4.754ms returns 0x16C
T93BC 002:412.257 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:412.293   Data:  1B FC 06 B0 80 BD C0 46 80 B5 86 B0 01 46 05 A8 ...
T93BC 002:412.357   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:419.899 - 7.640ms returns 0x294
T93BC 002:419.993 JLINK_HasError()
T93BC 002:420.095 JLINK_WriteReg(R0, 0x00005000)
T93BC 002:420.138 - 0.043ms returns 0
T93BC 002:420.174 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:420.210 - 0.035ms returns 0
T93BC 002:420.246 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:420.279 - 0.034ms returns 0
T93BC 002:420.314 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:420.348 - 0.033ms returns 0
T93BC 002:420.382 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:420.416 - 0.033ms returns 0
T93BC 002:420.450 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:420.484 - 0.033ms returns 0
T93BC 002:420.519 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:420.553 - 0.033ms returns 0
T93BC 002:420.588 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:420.622 - 0.034ms returns 0
T93BC 002:420.658 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:420.692 - 0.033ms returns 0
T93BC 002:420.728 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:420.762 - 0.034ms returns 0
T93BC 002:420.798 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:420.832 - 0.034ms returns 0
T93BC 002:420.868 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:420.902 - 0.034ms returns 0
T93BC 002:420.938 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:420.972 - 0.034ms returns 0
T93BC 002:421.008 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:421.043 - 0.035ms returns 0
T93BC 002:421.079 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:421.113 - 0.034ms returns 0
T93BC 002:421.148 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:421.182 - 0.034ms returns 0
T93BC 002:421.218 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:421.252 - 0.034ms returns 0
T93BC 002:421.288 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:421.322 - 0.034ms returns 0
T93BC 002:421.366 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:421.405 - 0.038ms returns 0
T93BC 002:421.441 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:421.475 - 0.034ms returns 0
T93BC 002:421.512 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:421.549 - 0.037ms returns 0x00000058
T93BC 002:421.585 JLINK_Go()
T93BC 002:421.633   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:425.641 - 4.055ms 
T93BC 002:425.696 JLINK_IsHalted()
T93BC 002:426.459 - 0.762ms returns FALSE
T93BC 002:426.521 JLINK_HasError()
T93BC 002:427.794 JLINK_IsHalted()
T93BC 002:428.444 - 0.649ms returns FALSE
T93BC 002:428.479 JLINK_HasError()
T93BC 002:430.103 JLINK_IsHalted()
T93BC 002:430.872 - 0.768ms returns FALSE
T93BC 002:430.913 JLINK_HasError()
T93BC 002:432.065 JLINK_IsHalted()
T93BC 002:435.680   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:436.540 - 4.474ms returns TRUE
T93BC 002:436.581 JLINK_ReadReg(R15 (PC))
T93BC 002:436.614 - 0.032ms returns 0x20200000
T93BC 002:436.644 JLINK_ClrBPEx(BPHandle = 0x00000058)
T93BC 002:436.672 - 0.028ms returns 0x00
T93BC 002:436.702 JLINK_ReadReg(R0)
T93BC 002:436.729 - 0.027ms returns 0x00000000
T93BC 002:437.516 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:437.560   Data:  FF E7 F8 E7 01 A8 01 78 02 48 FD F7 3E FE 02 B0 ...
T93BC 002:437.614   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:442.149 - 4.633ms returns 0x16C
T93BC 002:442.193 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:442.218   Data:  00 78 21 28 08 DB FF E7 04 49 00 20 08 70 04 48 ...
T93BC 002:442.260   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:449.822 - 7.628ms returns 0x294
T93BC 002:449.864 JLINK_HasError()
T93BC 002:449.883 JLINK_WriteReg(R0, 0x00005400)
T93BC 002:449.900 - 0.017ms returns 0
T93BC 002:449.916 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:449.930 - 0.014ms returns 0
T93BC 002:449.945 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:449.959 - 0.013ms returns 0
T93BC 002:449.975 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:449.988 - 0.013ms returns 0
T93BC 002:450.004 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:450.018 - 0.013ms returns 0
T93BC 002:450.034 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:450.047 - 0.013ms returns 0
T93BC 002:450.063 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:450.076 - 0.013ms returns 0
T93BC 002:450.092 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:450.106 - 0.013ms returns 0
T93BC 002:450.122 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:450.136 - 0.014ms returns 0
T93BC 002:450.152 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:450.165 - 0.013ms returns 0
T93BC 002:450.181 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:450.194 - 0.013ms returns 0
T93BC 002:450.210 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:450.224 - 0.013ms returns 0
T93BC 002:450.240 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:450.253 - 0.014ms returns 0
T93BC 002:450.269 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:450.283 - 0.014ms returns 0
T93BC 002:450.299 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:450.313 - 0.013ms returns 0
T93BC 002:450.329 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:450.343 - 0.014ms returns 0
T93BC 002:450.358 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:450.372 - 0.013ms returns 0
T93BC 002:450.388 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:450.401 - 0.014ms returns 0
T93BC 002:450.417 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:450.431 - 0.013ms returns 0
T93BC 002:450.447 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:450.460 - 0.013ms returns 0
T93BC 002:450.476 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:450.491 - 0.015ms returns 0x00000059
T93BC 002:450.507 JLINK_Go()
T93BC 002:450.526   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:454.945 - 4.436ms 
T93BC 002:455.038 JLINK_IsHalted()
T93BC 002:455.676 - 0.637ms returns FALSE
T93BC 002:455.724 JLINK_HasError()
T93BC 002:457.316 JLINK_IsHalted()
T93BC 002:458.058 - 0.739ms returns FALSE
T93BC 002:458.108 JLINK_HasError()
T93BC 002:459.337 JLINK_IsHalted()
T93BC 002:460.004 - 0.667ms returns FALSE
T93BC 002:460.044 JLINK_HasError()
T93BC 002:461.331 JLINK_IsHalted()
T93BC 002:464.823   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:465.591 - 4.259ms returns TRUE
T93BC 002:465.657 JLINK_ReadReg(R15 (PC))
T93BC 002:465.698 - 0.040ms returns 0x20200000
T93BC 002:465.734 JLINK_ClrBPEx(BPHandle = 0x00000059)
T93BC 002:465.770 - 0.035ms returns 0x00
T93BC 002:465.806 JLINK_ReadReg(R0)
T93BC 002:465.840 - 0.034ms returns 0x00000000
T93BC 002:466.808 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:466.861   Data:  11 A8 02 90 01 78 0B 20 04 90 48 43 34 49 03 91 ...
T93BC 002:466.928   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:471.606 - 4.798ms returns 0x16C
T93BC 002:471.681 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:471.717   Data:  0D 46 0B 99 05 95 04 94 03 93 02 92 01 91 00 90 ...
T93BC 002:471.782   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:479.309 - 7.626ms returns 0x294
T93BC 002:479.392 JLINK_HasError()
T93BC 002:479.433 JLINK_WriteReg(R0, 0x00005800)
T93BC 002:479.472 - 0.039ms returns 0
T93BC 002:479.508 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:479.542 - 0.034ms returns 0
T93BC 002:479.577 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:479.611 - 0.033ms returns 0
T93BC 002:479.645 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:479.679 - 0.033ms returns 0
T93BC 002:479.714 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:479.747 - 0.033ms returns 0
T93BC 002:479.782 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:479.816 - 0.034ms returns 0
T93BC 002:479.851 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:479.885 - 0.033ms returns 0
T93BC 002:479.920 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:479.953 - 0.033ms returns 0
T93BC 002:479.988 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:480.022 - 0.033ms returns 0
T93BC 002:480.057 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:480.091 - 0.033ms returns 0
T93BC 002:480.126 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:480.160 - 0.034ms returns 0
T93BC 002:480.195 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:480.228 - 0.033ms returns 0
T93BC 002:480.278 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:480.312 - 0.034ms returns 0
T93BC 002:480.347 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:480.382 - 0.034ms returns 0
T93BC 002:480.417 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:480.451 - 0.034ms returns 0
T93BC 002:480.486 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:480.519 - 0.033ms returns 0
T93BC 002:480.554 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:480.588 - 0.033ms returns 0
T93BC 002:480.623 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:480.656 - 0.033ms returns 0
T93BC 002:480.690 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:480.724 - 0.033ms returns 0
T93BC 002:480.759 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:480.792 - 0.033ms returns 0
T93BC 002:480.828 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:480.865 - 0.037ms returns 0x0000005A
T93BC 002:480.900 JLINK_Go()
T93BC 002:480.948   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:485.235 - 4.333ms 
T93BC 002:485.334 JLINK_IsHalted()
T93BC 002:486.069 - 0.734ms returns FALSE
T93BC 002:486.119 JLINK_HasError()
T93BC 002:490.358 JLINK_IsHalted()
T93BC 002:491.107 - 0.748ms returns FALSE
T93BC 002:491.151 JLINK_HasError()
T93BC 002:493.285 JLINK_IsHalted()
T93BC 002:496.876   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:497.905 - 4.618ms returns TRUE
T93BC 002:497.987 JLINK_ReadReg(R15 (PC))
T93BC 002:498.030 - 0.043ms returns 0x20200000
T93BC 002:498.070 JLINK_ClrBPEx(BPHandle = 0x0000005A)
T93BC 002:498.107 - 0.037ms returns 0x00
T93BC 002:498.145 JLINK_ReadReg(R0)
T93BC 002:498.181 - 0.036ms returns 0x00000000
T93BC 002:499.289 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:499.356   Data:  FE F7 30 FB 01 F0 46 FD FF E7 FE E7 80 B5 86 B0 ...
T93BC 002:499.429   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:504.134 - 4.845ms returns 0x16C
T93BC 002:504.179 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:504.206   Data:  8C 05 20 20 80 B5 84 B0 03 AA 01 92 10 70 02 91 ...
T93BC 002:504.254   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:511.771 - 7.591ms returns 0x294
T93BC 002:511.837 JLINK_HasError()
T93BC 002:511.920 JLINK_WriteReg(R0, 0x00005C00)
T93BC 002:511.950 - 0.030ms returns 0
T93BC 002:511.976 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:512.000 - 0.024ms returns 0
T93BC 002:512.025 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:512.049 - 0.023ms returns 0
T93BC 002:512.074 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:512.099 - 0.024ms returns 0
T93BC 002:512.124 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:512.149 - 0.024ms returns 0
T93BC 002:512.175 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:512.199 - 0.024ms returns 0
T93BC 002:512.225 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:512.255 - 0.030ms returns 0
T93BC 002:512.281 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:512.306 - 0.024ms returns 0
T93BC 002:512.331 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:512.356 - 0.024ms returns 0
T93BC 002:512.381 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:512.406 - 0.024ms returns 0
T93BC 002:512.432 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:512.457 - 0.024ms returns 0
T93BC 002:512.483 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:512.508 - 0.025ms returns 0
T93BC 002:512.534 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:512.558 - 0.024ms returns 0
T93BC 002:512.584 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:512.610 - 0.025ms returns 0
T93BC 002:512.635 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:512.660 - 0.024ms returns 0
T93BC 002:512.685 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:512.710 - 0.024ms returns 0
T93BC 002:512.736 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:512.762 - 0.025ms returns 0
T93BC 002:512.787 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:512.811 - 0.024ms returns 0
T93BC 002:512.837 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:512.861 - 0.024ms returns 0
T93BC 002:512.887 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:512.912 - 0.024ms returns 0
T93BC 002:512.938 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:512.965 - 0.027ms returns 0x0000005B
T93BC 002:512.990 JLINK_Go()
T93BC 002:513.026   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:517.136 - 4.144ms 
T93BC 002:517.205 JLINK_IsHalted()
T93BC 002:517.950 - 0.743ms returns FALSE
T93BC 002:518.011 JLINK_HasError()
T93BC 002:519.444 JLINK_IsHalted()
T93BC 002:520.284 - 0.838ms returns FALSE
T93BC 002:520.345 JLINK_HasError()
T93BC 002:522.502 JLINK_IsHalted()
T93BC 002:523.280 - 0.777ms returns FALSE
T93BC 002:523.347 JLINK_HasError()
T93BC 002:525.053 JLINK_IsHalted()
T93BC 002:529.418   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:530.115 - 5.061ms returns TRUE
T93BC 002:530.138 JLINK_ReadReg(R15 (PC))
T93BC 002:530.160 - 0.021ms returns 0x20200000
T93BC 002:530.179 JLINK_ClrBPEx(BPHandle = 0x0000005B)
T93BC 002:530.198 - 0.019ms returns 0x00
T93BC 002:530.218 JLINK_ReadReg(R0)
T93BC 002:530.237 - 0.018ms returns 0x00000000
T93BC 002:530.794 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:530.823   Data:  DD F8 01 99 C8 61 09 99 03 91 08 68 02 90 C8 69 ...
T93BC 002:530.860   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:535.414 - 4.619ms returns 0x16C
T93BC 002:535.497 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:535.525   Data:  00 90 61 48 00 68 00 1D 00 F0 4A FF 00 28 01 D1 ...
T93BC 002:535.575   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:543.105 - 7.606ms returns 0x294
T93BC 002:543.202 JLINK_HasError()
T93BC 002:543.254 JLINK_WriteReg(R0, 0x00006000)
T93BC 002:543.300 - 0.045ms returns 0
T93BC 002:543.346 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:543.386 - 0.040ms returns 0
T93BC 002:543.437 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:543.478 - 0.040ms returns 0
T93BC 002:543.525 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:543.567 - 0.042ms returns 0
T93BC 002:543.616 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:543.658 - 0.042ms returns 0
T93BC 002:543.706 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:543.748 - 0.042ms returns 0
T93BC 002:543.797 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:543.838 - 0.041ms returns 0
T93BC 002:543.887 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:543.928 - 0.042ms returns 0
T93BC 002:543.977 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:544.125 - 0.148ms returns 0
T93BC 002:544.175 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:544.217 - 0.042ms returns 0
T93BC 002:544.265 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:544.308 - 0.042ms returns 0
T93BC 002:544.356 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:544.398 - 0.042ms returns 0
T93BC 002:544.447 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:544.518 - 0.071ms returns 0
T93BC 002:544.576 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:544.618 - 0.042ms returns 0
T93BC 002:544.667 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:544.709 - 0.042ms returns 0
T93BC 002:544.758 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:544.819 - 0.061ms returns 0
T93BC 002:544.869 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:544.912 - 0.043ms returns 0
T93BC 002:544.960 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:545.003 - 0.042ms returns 0
T93BC 002:545.051 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:545.093 - 0.042ms returns 0
T93BC 002:545.141 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:545.183 - 0.042ms returns 0
T93BC 002:545.232 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:545.277 - 0.045ms returns 0x0000005C
T93BC 002:545.325 JLINK_Go()
T93BC 002:545.383   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:549.526 - 4.198ms 
T93BC 002:549.687 JLINK_IsHalted()
T93BC 002:550.674 - 0.986ms returns FALSE
T93BC 002:550.758 JLINK_HasError()
T93BC 002:552.641 JLINK_IsHalted()
T93BC 002:553.407 - 0.765ms returns FALSE
T93BC 002:553.448 JLINK_HasError()
T93BC 002:555.624 JLINK_IsHalted()
T93BC 002:559.175   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:559.920 - 4.297ms returns TRUE
T93BC 002:559.952 JLINK_ReadReg(R15 (PC))
T93BC 002:559.981 - 0.028ms returns 0x20200000
T93BC 002:560.008 JLINK_ClrBPEx(BPHandle = 0x0000005C)
T93BC 002:560.035 - 0.027ms returns 0x00
T93BC 002:560.063 JLINK_ReadReg(R0)
T93BC 002:560.090 - 0.026ms returns 0x00000000
T93BC 002:560.792 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:560.832   Data:  FF E7 5C 20 00 F0 50 FA 06 90 06 98 00 28 08 D0 ...
T93BC 002:560.876   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:565.389 - 4.596ms returns 0x16C
T93BC 002:565.415 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:565.429   Data:  04 98 00 28 20 D0 FF E7 00 20 00 90 FF E7 00 98 ...
T93BC 002:565.453   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:572.871 - 7.456ms returns 0x294
T93BC 002:572.895 JLINK_HasError()
T93BC 002:572.911 JLINK_WriteReg(R0, 0x00006400)
T93BC 002:572.926 - 0.015ms returns 0
T93BC 002:572.940 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:572.954 - 0.013ms returns 0
T93BC 002:572.968 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:572.982 - 0.014ms returns 0
T93BC 002:572.996 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:573.010 - 0.014ms returns 0
T93BC 002:573.024 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:573.038 - 0.013ms returns 0
T93BC 002:573.057 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:573.071 - 0.013ms returns 0
T93BC 002:573.085 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:573.100 - 0.014ms returns 0
T93BC 002:573.115 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:573.129 - 0.014ms returns 0
T93BC 002:573.144 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:573.159 - 0.014ms returns 0
T93BC 002:573.174 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:573.188 - 0.014ms returns 0
T93BC 002:573.203 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:573.218 - 0.014ms returns 0
T93BC 002:573.233 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:573.247 - 0.014ms returns 0
T93BC 002:573.262 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:573.277 - 0.014ms returns 0
T93BC 002:573.292 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:573.306 - 0.015ms returns 0
T93BC 002:573.322 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:573.336 - 0.014ms returns 0
T93BC 002:573.351 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:573.366 - 0.014ms returns 0
T93BC 002:573.380 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:573.396 - 0.015ms returns 0
T93BC 002:573.411 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:573.426 - 0.015ms returns 0
T93BC 002:573.441 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:573.461 - 0.019ms returns 0
T93BC 002:573.476 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:573.490 - 0.014ms returns 0
T93BC 002:573.506 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:573.522 - 0.016ms returns 0x0000005D
T93BC 002:573.537 JLINK_Go()
T93BC 002:573.557   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:577.753 - 4.214ms 
T93BC 002:577.796 JLINK_IsHalted()
T93BC 002:578.501 - 0.704ms returns FALSE
T93BC 002:578.544 JLINK_HasError()
T93BC 002:581.591 JLINK_IsHalted()
T93BC 002:582.291 - 0.699ms returns FALSE
T93BC 002:582.325 JLINK_HasError()
T93BC 002:585.676 JLINK_IsHalted()
T93BC 002:589.236   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:590.024 - 4.347ms returns TRUE
T93BC 002:590.070 JLINK_ReadReg(R15 (PC))
T93BC 002:590.114 - 0.043ms returns 0x20200000
T93BC 002:590.158 JLINK_ClrBPEx(BPHandle = 0x0000005D)
T93BC 002:590.199 - 0.041ms returns 0x00
T93BC 002:590.242 JLINK_ReadReg(R0)
T93BC 002:590.283 - 0.041ms returns 0x00000000
T93BC 002:591.388 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:591.463   Data:  88 42 04 DB FF E7 01 98 40 1E 00 90 02 E0 02 98 ...
T93BC 002:591.582   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:596.283 - 4.894ms returns 0x16C
T93BC 002:596.362 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:596.427   Data:  01 98 C0 07 00 28 06 D0 FF E7 06 98 05 90 06 98 ...
T93BC 002:596.525   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:604.121 - 7.757ms returns 0x294
T93BC 002:604.286 JLINK_HasError()
T93BC 002:604.342 JLINK_WriteReg(R0, 0x00006800)
T93BC 002:604.387 - 0.045ms returns 0
T93BC 002:604.426 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:604.464 - 0.038ms returns 0
T93BC 002:604.503 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:604.541 - 0.037ms returns 0
T93BC 002:604.581 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:604.618 - 0.037ms returns 0
T93BC 002:604.657 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:604.696 - 0.038ms returns 0
T93BC 002:604.735 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:604.772 - 0.037ms returns 0
T93BC 002:604.811 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:604.849 - 0.037ms returns 0
T93BC 002:604.887 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:604.925 - 0.037ms returns 0
T93BC 002:604.963 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:605.001 - 0.037ms returns 0
T93BC 002:605.040 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:605.078 - 0.037ms returns 0
T93BC 002:605.117 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:605.154 - 0.037ms returns 0
T93BC 002:605.194 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:605.232 - 0.038ms returns 0
T93BC 002:605.271 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:605.308 - 0.037ms returns 0
T93BC 002:605.348 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:605.387 - 0.039ms returns 0
T93BC 002:605.426 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:605.464 - 0.037ms returns 0
T93BC 002:605.502 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:605.538 - 0.036ms returns 0
T93BC 002:605.574 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:605.610 - 0.035ms returns 0
T93BC 002:605.646 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:605.682 - 0.035ms returns 0
T93BC 002:605.719 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:605.754 - 0.035ms returns 0
T93BC 002:605.791 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:605.827 - 0.035ms returns 0
T93BC 002:605.864 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:605.902 - 0.038ms returns 0x0000005E
T93BC 002:605.938 JLINK_Go()
T93BC 002:605.987   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:610.175 - 4.236ms 
T93BC 002:610.254 JLINK_IsHalted()
T93BC 002:610.958 - 0.704ms returns FALSE
T93BC 002:611.009 JLINK_HasError()
T93BC 002:613.374 JLINK_IsHalted()
T93BC 002:614.185 - 0.810ms returns FALSE
T93BC 002:614.265 JLINK_HasError()
T93BC 002:618.312 JLINK_IsHalted()
T93BC 002:621.978   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:622.791 - 4.478ms returns TRUE
T93BC 002:622.861 JLINK_ReadReg(R15 (PC))
T93BC 002:622.925 - 0.063ms returns 0x20200000
T93BC 002:622.987 JLINK_ClrBPEx(BPHandle = 0x0000005E)
T93BC 002:623.047 - 0.060ms returns 0x00
T93BC 002:623.124 JLINK_ReadReg(R0)
T93BC 002:623.182 - 0.057ms returns 0x00000000
T93BC 002:625.109 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:625.214   Data:  01 98 00 78 FE F7 22 FB 00 28 05 D0 FF E7 09 A8 ...
T93BC 002:625.376   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:630.051 - 4.942ms returns 0x16C
T93BC 002:630.142 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:630.193   Data:  F4 92 00 00 80 B5 82 B0 0A 46 01 46 01 A8 01 70 ...
T93BC 002:630.309   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:637.868 - 7.723ms returns 0x294
T93BC 002:638.008 JLINK_HasError()
T93BC 002:638.095 JLINK_WriteReg(R0, 0x00006C00)
T93BC 002:638.170 - 0.075ms returns 0
T93BC 002:638.287 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:638.355 - 0.068ms returns 0
T93BC 002:638.431 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:638.504 - 0.073ms returns 0
T93BC 002:638.580 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:638.646 - 0.065ms returns 0
T93BC 002:638.721 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:638.789 - 0.067ms returns 0
T93BC 002:638.865 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:638.930 - 0.065ms returns 0
T93BC 002:639.003 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:639.061 - 0.058ms returns 0
T93BC 002:639.129 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:639.188 - 0.059ms returns 0
T93BC 002:639.294 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:639.355 - 0.061ms returns 0
T93BC 002:639.424 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:639.483 - 0.059ms returns 0
T93BC 002:639.552 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:639.612 - 0.060ms returns 0
T93BC 002:639.681 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:639.740 - 0.059ms returns 0
T93BC 002:639.809 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:639.868 - 0.059ms returns 0
T93BC 002:639.936 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:639.998 - 0.062ms returns 0
T93BC 002:640.066 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:640.125 - 0.059ms returns 0
T93BC 002:640.194 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:640.294 - 0.100ms returns 0
T93BC 002:640.358 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:640.412 - 0.054ms returns 0
T93BC 002:640.474 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:640.526 - 0.052ms returns 0
T93BC 002:640.588 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:640.642 - 0.053ms returns 0
T93BC 002:640.705 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:640.759 - 0.053ms returns 0
T93BC 002:640.822 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:640.879 - 0.057ms returns 0x0000005F
T93BC 002:640.942 JLINK_Go()
T93BC 002:641.015   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:645.162 - 4.219ms 
T93BC 002:645.336 JLINK_IsHalted()
T93BC 002:646.214 - 0.878ms returns FALSE
T93BC 002:646.308 JLINK_HasError()
T93BC 002:648.292 JLINK_IsHalted()
T93BC 002:649.165 - 0.872ms returns FALSE
T93BC 002:649.223 JLINK_HasError()
T93BC 002:651.288 JLINK_IsHalted()
T93BC 002:654.857   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:655.592 - 4.303ms returns TRUE
T93BC 002:655.648 JLINK_ReadReg(R15 (PC))
T93BC 002:655.697 - 0.049ms returns 0x20200000
T93BC 002:655.751 JLINK_ClrBPEx(BPHandle = 0x0000005F)
T93BC 002:655.796 - 0.045ms returns 0x00
T93BC 002:655.843 JLINK_ReadReg(R0)
T93BC 002:655.888 - 0.044ms returns 0x00000000
T93BC 002:659.091 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:659.156   Data:  00 00 34 C3 00 00 B4 43 00 00 B4 C3 82 B0 01 90 ...
T93BC 002:659.259   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:663.848 - 4.757ms returns 0x16C
T93BC 002:663.897 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:663.936   Data:  00 20 08 61 01 B0 70 47 84 B0 03 90 02 91 02 98 ...
T93BC 002:664.001   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:671.512 - 7.613ms returns 0x294
T93BC 002:671.652 JLINK_HasError()
T93BC 002:671.732 JLINK_WriteReg(R0, 0x00007000)
T93BC 002:671.802 - 0.070ms returns 0
T93BC 002:671.873 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:671.940 - 0.067ms returns 0
T93BC 002:672.008 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:672.062 - 0.053ms returns 0
T93BC 002:672.138 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:672.192 - 0.053ms returns 0
T93BC 002:672.268 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:672.324 - 0.056ms returns 0
T93BC 002:672.388 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:672.442 - 0.054ms returns 0
T93BC 002:672.504 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:672.557 - 0.053ms returns 0
T93BC 002:672.619 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:672.673 - 0.054ms returns 0
T93BC 002:672.736 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:672.790 - 0.054ms returns 0
T93BC 002:672.852 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:672.904 - 0.053ms returns 0
T93BC 002:672.967 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:673.021 - 0.054ms returns 0
T93BC 002:673.083 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:673.137 - 0.054ms returns 0
T93BC 002:673.200 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:673.280 - 0.080ms returns 0
T93BC 002:673.343 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:673.400 - 0.057ms returns 0
T93BC 002:673.457 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:673.506 - 0.049ms returns 0
T93BC 002:673.563 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:673.613 - 0.050ms returns 0
T93BC 002:673.669 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:673.719 - 0.050ms returns 0
T93BC 002:673.775 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:673.825 - 0.049ms returns 0
T93BC 002:673.882 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:673.931 - 0.050ms returns 0
T93BC 002:673.988 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:674.038 - 0.049ms returns 0
T93BC 002:674.096 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:674.148 - 0.052ms returns 0x00000060
T93BC 002:674.205 JLINK_Go()
T93BC 002:674.286   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:678.408 - 4.202ms 
T93BC 002:678.480 JLINK_IsHalted()
T93BC 002:679.248 - 0.767ms returns FALSE
T93BC 002:679.313 JLINK_HasError()
T93BC 002:681.275 JLINK_IsHalted()
T93BC 002:682.042 - 0.767ms returns FALSE
T93BC 002:682.091 JLINK_HasError()
T93BC 002:684.292 JLINK_IsHalted()
T93BC 002:687.907   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:688.790 - 4.497ms returns TRUE
T93BC 002:688.883 JLINK_ReadReg(R15 (PC))
T93BC 002:688.940 - 0.057ms returns 0x20200000
T93BC 002:689.003 JLINK_ClrBPEx(BPHandle = 0x00000060)
T93BC 002:689.055 - 0.052ms returns 0x00
T93BC 002:689.110 JLINK_ReadReg(R0)
T93BC 002:689.161 - 0.051ms returns 0x00000000
T93BC 002:690.686 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:690.768   Data:  A2 49 08 68 40 1E 08 60 FF F7 D6 F9 FF E7 FF F7 ...
T93BC 002:690.863   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:695.529 - 4.843ms returns 0x16C
T93BC 002:695.585 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:695.627   Data:  FF E7 07 98 08 99 88 42 00 D3 81 E0 0D 98 07 9A ...
T93BC 002:695.698   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:703.251 - 7.664ms returns 0x294
T93BC 002:703.390 JLINK_HasError()
T93BC 002:703.459 JLINK_WriteReg(R0, 0x00007400)
T93BC 002:703.526 - 0.067ms returns 0
T93BC 002:703.588 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:703.647 - 0.058ms returns 0
T93BC 002:703.707 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:703.765 - 0.058ms returns 0
T93BC 002:703.826 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:703.884 - 0.058ms returns 0
T93BC 002:703.944 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:704.001 - 0.057ms returns 0
T93BC 002:704.061 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:704.119 - 0.057ms returns 0
T93BC 002:704.179 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:704.236 - 0.057ms returns 0
T93BC 002:704.296 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:704.355 - 0.058ms returns 0
T93BC 002:705.370 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:705.428 - 0.058ms returns 0
T93BC 002:705.484 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:705.536 - 0.052ms returns 0
T93BC 002:705.590 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:705.644 - 0.053ms returns 0
T93BC 002:705.698 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:705.751 - 0.052ms returns 0
T93BC 002:705.806 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:705.858 - 0.052ms returns 0
T93BC 002:705.928 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:705.982 - 0.054ms returns 0
T93BC 002:706.037 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:706.089 - 0.052ms returns 0
T93BC 002:706.144 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:706.197 - 0.053ms returns 0
T93BC 002:706.252 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:706.304 - 0.052ms returns 0
T93BC 002:706.358 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:706.440 - 0.081ms returns 0
T93BC 002:706.495 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:706.548 - 0.052ms returns 0
T93BC 002:706.602 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:706.655 - 0.052ms returns 0
T93BC 002:706.712 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:706.768 - 0.056ms returns 0x00000061
T93BC 002:706.823 JLINK_Go()
T93BC 002:706.897   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:711.008 - 4.184ms 
T93BC 002:711.061 JLINK_IsHalted()
T93BC 002:711.747 - 0.685ms returns FALSE
T93BC 002:711.788 JLINK_HasError()
T93BC 002:724.399 JLINK_IsHalted()
T93BC 002:728.061   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:728.811 - 4.411ms returns TRUE
T93BC 002:728.867 JLINK_ReadReg(R15 (PC))
T93BC 002:728.928 - 0.060ms returns 0x20200000
T93BC 002:728.976 JLINK_ClrBPEx(BPHandle = 0x00000061)
T93BC 002:729.022 - 0.046ms returns 0x00
T93BC 002:729.071 JLINK_ReadReg(R0)
T93BC 002:729.120 - 0.048ms returns 0x00000000
T93BC 002:730.493 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:730.601   Data:  FE F7 F0 FC 01 20 02 90 03 E0 00 20 C0 43 02 90 ...
T93BC 002:730.718   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:735.831 - 5.338ms returns 0x16C
T93BC 002:735.944 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:735.995   Data:  02 90 07 99 C8 68 89 68 88 60 07 99 88 68 C9 68 ...
T93BC 002:736.093   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:743.709 - 7.764ms returns 0x294
T93BC 002:743.793 JLINK_HasError()
T93BC 002:743.845 JLINK_WriteReg(R0, 0x00007800)
T93BC 002:743.897 - 0.052ms returns 0
T93BC 002:743.945 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:743.990 - 0.045ms returns 0
T93BC 002:744.037 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:744.081 - 0.044ms returns 0
T93BC 002:744.127 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:744.177 - 0.049ms returns 0
T93BC 002:744.223 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:744.267 - 0.044ms returns 0
T93BC 002:744.313 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:744.371 - 0.057ms returns 0
T93BC 002:744.414 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:744.456 - 0.041ms returns 0
T93BC 002:744.498 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:744.539 - 0.041ms returns 0
T93BC 002:744.583 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:744.624 - 0.041ms returns 0
T93BC 002:744.667 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:744.708 - 0.041ms returns 0
T93BC 002:744.751 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:744.792 - 0.041ms returns 0
T93BC 002:744.835 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:744.875 - 0.040ms returns 0
T93BC 002:744.918 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:744.959 - 0.042ms returns 0
T93BC 002:744.999 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:745.039 - 0.040ms returns 0
T93BC 002:745.080 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:745.118 - 0.038ms returns 0
T93BC 002:745.159 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:745.198 - 0.039ms returns 0
T93BC 002:745.238 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:745.277 - 0.039ms returns 0
T93BC 002:745.317 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:745.355 - 0.037ms returns 0
T93BC 002:745.394 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:745.529 - 0.135ms returns 0
T93BC 002:745.571 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:745.610 - 0.038ms returns 0
T93BC 002:745.651 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:745.692 - 0.041ms returns 0x00000062
T93BC 002:745.732 JLINK_Go()
T93BC 002:745.806   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:750.126 - 4.392ms 
T93BC 002:750.210 JLINK_IsHalted()
T93BC 002:750.866 - 0.655ms returns FALSE
T93BC 002:750.926 JLINK_HasError()
T93BC 002:752.439 JLINK_IsHalted()
T93BC 002:753.150 - 0.710ms returns FALSE
T93BC 002:753.201 JLINK_HasError()
T93BC 002:754.583 JLINK_IsHalted()
T93BC 002:755.467 - 0.883ms returns FALSE
T93BC 002:755.529 JLINK_HasError()
T93BC 002:757.497 JLINK_IsHalted()
T93BC 002:761.152   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:762.053 - 4.555ms returns TRUE
T93BC 002:762.127 JLINK_ReadReg(R15 (PC))
T93BC 002:762.194 - 0.066ms returns 0x20200000
T93BC 002:762.255 JLINK_ClrBPEx(BPHandle = 0x00000062)
T93BC 002:762.328 - 0.073ms returns 0x00
T93BC 002:762.411 JLINK_ReadReg(R0)
T93BC 002:762.472 - 0.060ms returns 0x00000000
T93BC 002:764.230 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:764.342   Data:  08 18 58 61 07 98 C0 6A 42 43 88 58 40 1C 88 50 ...
T93BC 002:764.472   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:769.733 - 5.502ms returns 0x16C
T93BC 002:769.850 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:769.907   Data:  03 0B 8B 42 01 D3 0B 03 C0 1A 52 41 C3 0A 8B 42 ...
T93BC 002:770.013   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:778.253 - 8.401ms returns 0x294
T93BC 002:778.351 JLINK_HasError()
T93BC 002:778.407 JLINK_WriteReg(R0, 0x00007C00)
T93BC 002:778.462 - 0.055ms returns 0
T93BC 002:778.513 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:778.574 - 0.061ms returns 0
T93BC 002:778.624 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:778.671 - 0.047ms returns 0
T93BC 002:778.720 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:778.769 - 0.049ms returns 0
T93BC 002:778.818 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:778.865 - 0.046ms returns 0
T93BC 002:778.914 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:778.961 - 0.047ms returns 0
T93BC 002:779.014 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:779.064 - 0.051ms returns 0
T93BC 002:779.118 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:779.170 - 0.051ms returns 0
T93BC 002:779.253 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:779.304 - 0.051ms returns 0
T93BC 002:779.357 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:779.407 - 0.050ms returns 0
T93BC 002:779.460 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:779.512 - 0.052ms returns 0
T93BC 002:779.566 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:779.617 - 0.051ms returns 0
T93BC 002:779.671 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:779.722 - 0.051ms returns 0
T93BC 002:779.776 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:779.830 - 0.054ms returns 0
T93BC 002:779.884 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:779.935 - 0.051ms returns 0
T93BC 002:779.988 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:780.039 - 0.051ms returns 0
T93BC 002:780.097 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:780.144 - 0.050ms returns 0
T93BC 002:780.214 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:780.262 - 0.048ms returns 0
T93BC 002:780.326 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:780.375 - 0.049ms returns 0
T93BC 002:780.431 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:780.480 - 0.049ms returns 0
T93BC 002:780.538 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:780.590 - 0.053ms returns 0x00000063
T93BC 002:780.646 JLINK_Go()
T93BC 002:780.712   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:785.318 - 4.670ms 
T93BC 002:785.429 JLINK_IsHalted()
T93BC 002:786.269 - 0.839ms returns FALSE
T93BC 002:786.386 JLINK_HasError()
T93BC 002:788.374 JLINK_IsHalted()
T93BC 002:789.375 - 0.998ms returns FALSE
T93BC 002:789.531 JLINK_HasError()
T93BC 002:791.431 JLINK_IsHalted()
T93BC 002:795.417   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:796.427 - 4.994ms returns TRUE
T93BC 002:796.578 JLINK_ReadReg(R15 (PC))
T93BC 002:796.662 - 0.083ms returns 0x20200000
T93BC 002:796.739 JLINK_ClrBPEx(BPHandle = 0x00000063)
T93BC 002:796.832 - 0.092ms returns 0x00
T93BC 002:796.906 JLINK_ReadReg(R0)
T93BC 002:796.976 - 0.070ms returns 0x00000000
T93BC 002:800.663 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:800.786   Data:  2C 43 D3 1A DC 40 05 19 10 D2 28 0A 02 D3 40 1C ...
T93BC 002:800.894   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:805.806 - 5.068ms returns 0x16C
T93BC 002:805.895 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:805.950   Data:  06 D0 E4 07 20 43 40 19 70 BD 01 25 A8 43 F4 E7 ...
T93BC 002:806.027   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:813.905 - 8.007ms returns 0x294
T93BC 002:814.100 JLINK_HasError()
T93BC 002:814.326 JLINK_WriteReg(R0, 0x00008000)
T93BC 002:814.408 - 0.082ms returns 0
T93BC 002:814.475 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:814.539 - 0.063ms returns 0
T93BC 002:814.924 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:815.028 - 0.104ms returns 0
T93BC 002:815.097 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:815.162 - 0.064ms returns 0
T93BC 002:815.228 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:815.291 - 0.063ms returns 0
T93BC 002:815.357 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:815.422 - 0.064ms returns 0
T93BC 002:815.488 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:815.552 - 0.064ms returns 0
T93BC 002:815.618 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:815.680 - 0.063ms returns 0
T93BC 002:815.746 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:815.809 - 0.063ms returns 0
T93BC 002:815.875 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:815.938 - 0.063ms returns 0
T93BC 002:816.002 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:816.065 - 0.062ms returns 0
T93BC 002:816.131 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:816.222 - 0.091ms returns 0
T93BC 002:816.282 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:816.338 - 0.056ms returns 0
T93BC 002:816.398 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:816.456 - 0.058ms returns 0
T93BC 002:816.515 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:816.571 - 0.056ms returns 0
T93BC 002:816.630 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:816.688 - 0.058ms returns 0
T93BC 002:816.746 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:816.804 - 0.057ms returns 0
T93BC 002:816.864 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:816.921 - 0.057ms returns 0
T93BC 002:816.981 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:817.038 - 0.057ms returns 0
T93BC 002:817.097 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:817.154 - 0.057ms returns 0
T93BC 002:817.211 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:817.268 - 0.058ms returns 0x00000064
T93BC 002:817.322 JLINK_Go()
T93BC 002:817.396   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:821.836 - 4.513ms 
T93BC 002:821.898 JLINK_IsHalted()
T93BC 002:822.696 - 0.797ms returns FALSE
T93BC 002:822.758 JLINK_HasError()
T93BC 002:826.827 JLINK_IsHalted()
T93BC 002:827.736 - 0.907ms returns FALSE
T93BC 002:827.844 JLINK_HasError()
T93BC 002:829.786 JLINK_IsHalted()
T93BC 002:833.510   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:834.633 - 4.845ms returns TRUE
T93BC 002:834.704 JLINK_ReadReg(R15 (PC))
T93BC 002:834.759 - 0.055ms returns 0x20200000
T93BC 002:834.811 JLINK_ClrBPEx(BPHandle = 0x00000064)
T93BC 002:834.860 - 0.049ms returns 0x00
T93BC 002:834.910 JLINK_ReadReg(R0)
T93BC 002:834.958 - 0.047ms returns 0x00000000
T93BC 002:836.101 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:836.166   Data:  00 00 00 00 00 00 00 00 00 00 00 A1 A3 00 00 00 ...
T93BC 002:836.244   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:841.008 - 4.907ms returns 0x16C
T93BC 002:841.111 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:841.169   Data:  03 00 36 49 49 49 36 00 06 49 49 29 1E 00 00 36 ...
T93BC 002:841.279   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:848.806 - 7.694ms returns 0x294
T93BC 002:849.013 JLINK_HasError()
T93BC 002:849.174 JLINK_WriteReg(R0, 0x00008400)
T93BC 002:849.233 - 0.059ms returns 0
T93BC 002:849.283 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:849.330 - 0.047ms returns 0
T93BC 002:849.380 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:849.421 - 0.042ms returns 0
T93BC 002:849.468 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:849.511 - 0.043ms returns 0
T93BC 002:849.556 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:849.600 - 0.043ms returns 0
T93BC 002:849.646 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:849.689 - 0.043ms returns 0
T93BC 002:849.734 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:849.777 - 0.043ms returns 0
T93BC 002:849.823 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:849.878 - 0.055ms returns 0
T93BC 002:849.937 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:849.980 - 0.044ms returns 0
T93BC 002:850.026 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:850.068 - 0.042ms returns 0
T93BC 002:850.115 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:850.159 - 0.043ms returns 0
T93BC 002:850.204 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:850.247 - 0.043ms returns 0
T93BC 002:850.292 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:850.336 - 0.044ms returns 0
T93BC 002:850.381 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:850.425 - 0.045ms returns 0
T93BC 002:850.472 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:850.513 - 0.042ms returns 0
T93BC 002:850.556 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:850.598 - 0.042ms returns 0
T93BC 002:850.641 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:850.681 - 0.040ms returns 0
T93BC 002:850.726 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:850.766 - 0.040ms returns 0
T93BC 002:850.825 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:850.867 - 0.042ms returns 0
T93BC 002:850.908 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:850.950 - 0.041ms returns 0
T93BC 002:850.995 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:851.038 - 0.044ms returns 0x00000065
T93BC 002:851.080 JLINK_Go()
T93BC 002:851.136   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:855.298 - 4.218ms 
T93BC 002:855.343 JLINK_IsHalted()
T93BC 002:856.036 - 0.693ms returns FALSE
T93BC 002:856.078 JLINK_HasError()
T93BC 002:857.596 JLINK_IsHalted()
T93BC 002:858.296 - 0.700ms returns FALSE
T93BC 002:858.342 JLINK_HasError()
T93BC 002:859.573 JLINK_IsHalted()
T93BC 002:860.280 - 0.707ms returns FALSE
T93BC 002:860.334 JLINK_HasError()
T93BC 002:861.559 JLINK_IsHalted()
T93BC 002:865.378   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:866.406 - 4.845ms returns TRUE
T93BC 002:866.485 JLINK_ReadReg(R15 (PC))
T93BC 002:866.542 - 0.056ms returns 0x20200000
T93BC 002:866.595 JLINK_ClrBPEx(BPHandle = 0x00000065)
T93BC 002:866.645 - 0.050ms returns 0x00
T93BC 002:866.697 JLINK_ReadReg(R0)
T93BC 002:866.746 - 0.049ms returns 0x00000000
T93BC 002:868.540 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:868.626   Data:  30 30 00 00 00 00 00 00 00 00 00 80 60 18 04 00 ...
T93BC 002:868.706   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:873.453 - 4.914ms returns 0x16C
T93BC 002:873.510 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:873.555   Data:  08 10 00 20 3F 20 20 23 20 18 00 08 F8 88 88 E8 ...
T93BC 002:873.634   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:881.254 - 7.741ms returns 0x294
T93BC 002:881.488 JLINK_HasError()
T93BC 002:881.562 JLINK_WriteReg(R0, 0x00008800)
T93BC 002:881.628 - 0.065ms returns 0
T93BC 002:881.691 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:881.768 - 0.077ms returns 0
T93BC 002:881.855 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:881.910 - 0.054ms returns 0
T93BC 002:881.973 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:882.027 - 0.054ms returns 0
T93BC 002:882.090 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:882.150 - 0.060ms returns 0
T93BC 002:882.207 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:882.262 - 0.054ms returns 0
T93BC 002:882.319 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:882.371 - 0.052ms returns 0
T93BC 002:882.430 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:882.484 - 0.054ms returns 0
T93BC 002:882.543 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:882.597 - 0.054ms returns 0
T93BC 002:882.659 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:882.713 - 0.054ms returns 0
T93BC 002:882.786 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:882.838 - 0.052ms returns 0
T93BC 002:882.896 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:882.946 - 0.051ms returns 0
T93BC 002:883.006 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:883.056 - 0.050ms returns 0
T93BC 002:883.116 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:883.167 - 0.051ms returns 0
T93BC 002:883.219 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:883.264 - 0.045ms returns 0
T93BC 002:883.317 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:883.362 - 0.046ms returns 0
T93BC 002:883.423 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:883.467 - 0.044ms returns 0
T93BC 002:883.519 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:883.564 - 0.045ms returns 0
T93BC 002:883.617 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:883.662 - 0.045ms returns 0
T93BC 002:883.714 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:883.783 - 0.069ms returns 0
T93BC 002:883.839 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:883.927 - 0.088ms returns 0x00000066
T93BC 002:883.995 JLINK_Go()
T93BC 002:884.069   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:888.220 - 4.224ms 
T93BC 002:888.316 JLINK_IsHalted()
T93BC 002:888.997 - 0.680ms returns FALSE
T93BC 002:889.054 JLINK_HasError()
T93BC 002:892.827 JLINK_IsHalted()
T93BC 002:893.645 - 0.817ms returns FALSE
T93BC 002:893.715 JLINK_HasError()
T93BC 002:899.380 JLINK_IsHalted()
T93BC 002:903.195   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:903.965 - 4.584ms returns TRUE
T93BC 002:904.018 JLINK_ReadReg(R15 (PC))
T93BC 002:904.068 - 0.049ms returns 0x20200000
T93BC 002:904.115 JLINK_ClrBPEx(BPHandle = 0x00000066)
T93BC 002:904.163 - 0.047ms returns 0x00
T93BC 002:904.210 JLINK_ReadReg(R0)
T93BC 002:904.255 - 0.045ms returns 0x00000000
T93BC 002:905.358 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:905.437   Data:  20 3F 21 00 20 3F 20 00 00 80 80 80 80 00 00 00 ...
T93BC 002:905.530   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:910.183 - 4.824ms returns 0x16C
T93BC 002:910.261 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:910.314   Data:  10 00 00 00 00 01 00 00 03 00 00 00 00 00 00 00 ...
T93BC 002:910.404   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:917.968 - 7.706ms returns 0x294
T93BC 002:918.045 JLINK_HasError()
T93BC 002:918.095 JLINK_WriteReg(R0, 0x00008C00)
T93BC 002:918.136 - 0.042ms returns 0
T93BC 002:918.179 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:918.214 - 0.034ms returns 0
T93BC 002:918.253 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:918.288 - 0.035ms returns 0
T93BC 002:918.327 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:918.362 - 0.035ms returns 0
T93BC 002:918.407 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:918.442 - 0.034ms returns 0
T93BC 002:918.481 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:918.515 - 0.034ms returns 0
T93BC 002:918.555 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:918.589 - 0.034ms returns 0
T93BC 002:918.629 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:918.663 - 0.034ms returns 0
T93BC 002:918.702 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:918.737 - 0.034ms returns 0
T93BC 002:918.776 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:918.811 - 0.034ms returns 0
T93BC 002:918.850 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:918.884 - 0.034ms returns 0
T93BC 002:918.924 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:918.958 - 0.034ms returns 0
T93BC 002:918.997 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:919.032 - 0.034ms returns 0
T93BC 002:919.072 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:919.109 - 0.038ms returns 0
T93BC 002:919.149 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:919.183 - 0.034ms returns 0
T93BC 002:919.223 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:919.257 - 0.034ms returns 0
T93BC 002:919.297 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:919.331 - 0.035ms returns 0
T93BC 002:919.370 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:919.405 - 0.034ms returns 0
T93BC 002:919.445 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:919.479 - 0.034ms returns 0
T93BC 002:919.519 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:919.553 - 0.034ms returns 0
T93BC 002:919.593 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:919.630 - 0.037ms returns 0x00000067
T93BC 002:919.746 JLINK_Go()
T93BC 002:919.796   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:923.862 - 4.115ms 
T93BC 002:923.912 JLINK_IsHalted()
T93BC 002:924.670 - 0.757ms returns FALSE
T93BC 002:924.724 JLINK_HasError()
T93BC 002:929.452 JLINK_IsHalted()
T93BC 002:930.263 - 0.811ms returns FALSE
T93BC 002:930.313 JLINK_HasError()
T93BC 002:932.439 JLINK_IsHalted()
T93BC 002:936.383   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:937.294 - 4.854ms returns TRUE
T93BC 002:937.359 JLINK_ReadReg(R15 (PC))
T93BC 002:937.425 - 0.065ms returns 0x20200000
T93BC 002:937.482 JLINK_ClrBPEx(BPHandle = 0x00000067)
T93BC 002:937.536 - 0.053ms returns 0x00
T93BC 002:937.591 JLINK_ReadReg(R0)
T93BC 002:937.643 - 0.052ms returns 0x00000000
T93BC 002:939.470 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:939.609   Data:  25 64 20 25 64 20 25 64 20 25 64 20 25 64 20 25 ...
T93BC 002:939.728   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:944.560 - 5.090ms returns 0x16C
T93BC 002:944.672 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:944.734   Data:  25 30 31 64 25 25 0D 0A 00 2A 2A 2A 2A 2A 2A 2A ...
T93BC 002:944.838   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:952.473 - 7.801ms returns 0x294
T93BC 002:952.588 JLINK_HasError()
T93BC 002:952.652 JLINK_WriteReg(R0, 0x00009000)
T93BC 002:952.708 - 0.055ms returns 0
T93BC 002:952.765 JLINK_WriteReg(R1, 0x00000400)
T93BC 002:952.815 - 0.049ms returns 0
T93BC 002:952.872 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:952.921 - 0.049ms returns 0
T93BC 002:952.978 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:953.039 - 0.061ms returns 0
T93BC 002:953.098 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:953.147 - 0.049ms returns 0
T93BC 002:953.208 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:953.263 - 0.055ms returns 0
T93BC 002:953.325 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:953.390 - 0.065ms returns 0
T93BC 002:953.458 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:953.512 - 0.053ms returns 0
T93BC 002:953.574 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:953.627 - 0.053ms returns 0
T93BC 002:953.688 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:953.741 - 0.053ms returns 0
T93BC 002:953.804 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:953.859 - 0.055ms returns 0
T93BC 002:953.921 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:953.974 - 0.053ms returns 0
T93BC 002:954.036 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:954.088 - 0.052ms returns 0
T93BC 002:954.145 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:954.195 - 0.051ms returns 0
T93BC 002:954.252 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:954.301 - 0.049ms returns 0
T93BC 002:954.381 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:954.431 - 0.050ms returns 0
T93BC 002:954.488 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:954.537 - 0.049ms returns 0
T93BC 002:954.594 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:954.642 - 0.049ms returns 0
T93BC 002:954.699 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:954.748 - 0.049ms returns 0
T93BC 002:954.805 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:954.853 - 0.048ms returns 0
T93BC 002:954.911 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:954.962 - 0.052ms returns 0x00000068
T93BC 002:955.017 JLINK_Go()
T93BC 002:955.080   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:959.228 - 4.209ms 
T93BC 002:959.323 JLINK_IsHalted()
T93BC 002:959.964 - 0.640ms returns FALSE
T93BC 002:960.026 JLINK_HasError()
T93BC 002:962.021 JLINK_IsHalted()
T93BC 002:962.791 - 0.767ms returns FALSE
T93BC 002:962.869 JLINK_HasError()
T93BC 002:964.019 JLINK_IsHalted()
T93BC 002:965.203 - 1.181ms returns FALSE
T93BC 002:965.347 JLINK_HasError()
T93BC 002:966.816 JLINK_IsHalted()
T93BC 002:970.808   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 002:971.661 - 4.845ms returns TRUE
T93BC 002:971.805 JLINK_ReadReg(R15 (PC))
T93BC 002:971.862 - 0.057ms returns 0x20200000
T93BC 002:971.914 JLINK_ClrBPEx(BPHandle = 0x00000068)
T93BC 002:971.964 - 0.050ms returns 0x00
T93BC 002:972.014 JLINK_ReadReg(R0)
T93BC 002:972.062 - 0.047ms returns 0x00000000
T93BC 002:974.013 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T93BC 002:974.142   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T93BC 002:974.232   CPU_WriteMem(364 bytes @ 0x20200294)
T93BC 002:978.983 - 4.970ms returns 0x16C
T93BC 002:979.051 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T93BC 002:979.089   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T93BC 002:979.156   CPU_WriteMem(660 bytes @ 0x20200400)
T93BC 002:986.725 - 7.672ms returns 0x294
T93BC 002:986.865 JLINK_HasError()
T93BC 002:986.930 JLINK_WriteReg(R0, 0x00009400)
T93BC 002:986.998 - 0.068ms returns 0
T93BC 002:987.064 JLINK_WriteReg(R1, 0x00000180)
T93BC 002:987.152 - 0.088ms returns 0
T93BC 002:987.217 JLINK_WriteReg(R2, 0x20200294)
T93BC 002:987.275 - 0.058ms returns 0
T93BC 002:987.348 JLINK_WriteReg(R3, 0x00000000)
T93BC 002:987.411 - 0.063ms returns 0
T93BC 002:987.495 JLINK_WriteReg(R4, 0x00000000)
T93BC 002:987.562 - 0.066ms returns 0
T93BC 002:987.631 JLINK_WriteReg(R5, 0x00000000)
T93BC 002:987.691 - 0.060ms returns 0
T93BC 002:987.762 JLINK_WriteReg(R6, 0x00000000)
T93BC 002:987.820 - 0.058ms returns 0
T93BC 002:987.888 JLINK_WriteReg(R7, 0x00000000)
T93BC 002:987.994 - 0.106ms returns 0
T93BC 002:988.064 JLINK_WriteReg(R8, 0x00000000)
T93BC 002:988.121 - 0.058ms returns 0
T93BC 002:988.190 JLINK_WriteReg(R9, 0x20200290)
T93BC 002:988.248 - 0.057ms returns 0
T93BC 002:988.312 JLINK_WriteReg(R10, 0x00000000)
T93BC 002:988.366 - 0.054ms returns 0
T93BC 002:988.427 JLINK_WriteReg(R11, 0x00000000)
T93BC 002:988.481 - 0.053ms returns 0
T93BC 002:988.542 JLINK_WriteReg(R12, 0x00000000)
T93BC 002:988.595 - 0.053ms returns 0
T93BC 002:988.657 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 002:988.712 - 0.055ms returns 0
T93BC 002:988.773 JLINK_WriteReg(R14, 0x20200001)
T93BC 002:988.825 - 0.052ms returns 0
T93BC 002:988.886 JLINK_WriteReg(R15 (PC), 0x20200120)
T93BC 002:988.939 - 0.053ms returns 0
T93BC 002:989.000 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 002:989.052 - 0.053ms returns 0
T93BC 002:989.229 JLINK_WriteReg(MSP, 0x20208000)
T93BC 002:989.340 - 0.111ms returns 0
T93BC 002:989.395 JLINK_WriteReg(PSP, 0x20208000)
T93BC 002:989.446 - 0.051ms returns 0
T93BC 002:989.510 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 002:989.563 - 0.053ms returns 0
T93BC 002:989.618 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 002:989.675 - 0.057ms returns 0x00000069
T93BC 002:989.731 JLINK_Go()
T93BC 002:989.803   CPU_ReadMem(4 bytes @ 0x********)
T93BC 002:994.180 - 4.448ms 
T93BC 002:994.231 JLINK_IsHalted()
T93BC 002:994.907 - 0.676ms returns FALSE
T93BC 002:994.950 JLINK_HasError()
T93BC 002:996.508 JLINK_IsHalted()
T93BC 003:000.013   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:000.879 - 4.371ms returns TRUE
T93BC 003:000.928 JLINK_ReadReg(R15 (PC))
T93BC 003:000.976 - 0.047ms returns 0x20200000
T93BC 003:001.021 JLINK_ClrBPEx(BPHandle = 0x00000069)
T93BC 003:001.062 - 0.040ms returns 0x00
T93BC 003:001.102 JLINK_ReadReg(R0)
T93BC 003:001.141 - 0.039ms returns 0x00000000
T93BC 003:001.183 JLINK_HasError()
T93BC 003:001.224 JLINK_WriteReg(R0, 0x00000002)
T93BC 003:001.263 - 0.039ms returns 0
T93BC 003:001.306 JLINK_WriteReg(R1, 0x00000180)
T93BC 003:001.347 - 0.041ms returns 0
T93BC 003:001.390 JLINK_WriteReg(R2, 0x20200294)
T93BC 003:001.430 - 0.040ms returns 0
T93BC 003:001.473 JLINK_WriteReg(R3, 0x00000000)
T93BC 003:001.515 - 0.041ms returns 0
T93BC 003:001.568 JLINK_WriteReg(R4, 0x00000000)
T93BC 003:001.610 - 0.042ms returns 0
T93BC 003:001.654 JLINK_WriteReg(R5, 0x00000000)
T93BC 003:001.696 - 0.042ms returns 0
T93BC 003:001.739 JLINK_WriteReg(R6, 0x00000000)
T93BC 003:001.780 - 0.041ms returns 0
T93BC 003:001.823 JLINK_WriteReg(R7, 0x00000000)
T93BC 003:001.864 - 0.041ms returns 0
T93BC 003:001.907 JLINK_WriteReg(R8, 0x00000000)
T93BC 003:001.949 - 0.041ms returns 0
T93BC 003:001.992 JLINK_WriteReg(R9, 0x20200290)
T93BC 003:002.033 - 0.041ms returns 0
T93BC 003:002.076 JLINK_WriteReg(R10, 0x00000000)
T93BC 003:002.120 - 0.043ms returns 0
T93BC 003:002.166 JLINK_WriteReg(R11, 0x00000000)
T93BC 003:002.210 - 0.044ms returns 0
T93BC 003:002.257 JLINK_WriteReg(R12, 0x00000000)
T93BC 003:002.301 - 0.044ms returns 0
T93BC 003:002.347 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 003:002.393 - 0.046ms returns 0
T93BC 003:002.439 JLINK_WriteReg(R14, 0x20200001)
T93BC 003:002.484 - 0.045ms returns 0
T93BC 003:002.610 JLINK_WriteReg(R15 (PC), 0x20200094)
T93BC 003:002.658 - 0.127ms returns 0
T93BC 003:002.715 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 003:002.760 - 0.045ms returns 0
T93BC 003:002.807 JLINK_WriteReg(MSP, 0x20208000)
T93BC 003:002.852 - 0.045ms returns 0
T93BC 003:002.900 JLINK_WriteReg(PSP, 0x20208000)
T93BC 003:002.950 - 0.049ms returns 0
T93BC 003:002.998 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 003:003.039 - 0.041ms returns 0
T93BC 003:003.084 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 003:003.127 - 0.043ms returns 0x0000006A
T93BC 003:003.170 JLINK_Go()
T93BC 003:003.224   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:007.431 - 4.260ms 
T93BC 003:007.503 JLINK_IsHalted()
T93BC 003:011.058   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:011.830 - 4.326ms returns TRUE
T93BC 003:011.876 JLINK_ReadReg(R15 (PC))
T93BC 003:011.916 - 0.039ms returns 0x20200000
T93BC 003:011.954 JLINK_ClrBPEx(BPHandle = 0x0000006A)
T93BC 003:011.991 - 0.037ms returns 0x00
T93BC 003:012.029 JLINK_ReadReg(R0)
T93BC 003:012.064 - 0.035ms returns 0x00000000
T93BC 003:082.715 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T93BC 003:082.773   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T93BC 003:082.867   CPU_WriteMem(660 bytes @ 0x20200000)
T93BC 003:090.594 - 7.876ms returns 0x294
T93BC 003:090.945 JLINK_HasError()
T93BC 003:091.020 JLINK_WriteReg(R0, 0x00000000)
T93BC 003:091.088 - 0.068ms returns 0
T93BC 003:091.164 JLINK_WriteReg(R1, 0x01F78A40)
T93BC 003:091.228 - 0.064ms returns 0
T93BC 003:091.294 JLINK_WriteReg(R2, 0x00000003)
T93BC 003:091.357 - 0.064ms returns 0
T93BC 003:091.425 JLINK_WriteReg(R3, 0x00000000)
T93BC 003:091.490 - 0.064ms returns 0
T93BC 003:091.563 JLINK_WriteReg(R4, 0x00000000)
T93BC 003:091.626 - 0.063ms returns 0
T93BC 003:091.692 JLINK_WriteReg(R5, 0x00000000)
T93BC 003:091.755 - 0.063ms returns 0
T93BC 003:091.819 JLINK_WriteReg(R6, 0x00000000)
T93BC 003:091.882 - 0.063ms returns 0
T93BC 003:091.948 JLINK_WriteReg(R7, 0x00000000)
T93BC 003:092.010 - 0.062ms returns 0
T93BC 003:092.070 JLINK_WriteReg(R8, 0x00000000)
T93BC 003:092.127 - 0.057ms returns 0
T93BC 003:092.186 JLINK_WriteReg(R9, 0x20200290)
T93BC 003:092.242 - 0.055ms returns 0
T93BC 003:092.333 JLINK_WriteReg(R10, 0x00000000)
T93BC 003:092.391 - 0.058ms returns 0
T93BC 003:092.450 JLINK_WriteReg(R11, 0x00000000)
T93BC 003:092.506 - 0.056ms returns 0
T93BC 003:092.565 JLINK_WriteReg(R12, 0x00000000)
T93BC 003:092.638 - 0.072ms returns 0
T93BC 003:092.699 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 003:092.757 - 0.058ms returns 0
T93BC 003:092.817 JLINK_WriteReg(R14, 0x20200001)
T93BC 003:092.874 - 0.057ms returns 0
T93BC 003:092.932 JLINK_WriteReg(R15 (PC), 0x20200038)
T93BC 003:092.988 - 0.056ms returns 0
T93BC 003:093.042 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 003:093.093 - 0.051ms returns 0
T93BC 003:093.147 JLINK_WriteReg(MSP, 0x20208000)
T93BC 003:093.198 - 0.051ms returns 0
T93BC 003:093.251 JLINK_WriteReg(PSP, 0x20208000)
T93BC 003:093.302 - 0.051ms returns 0
T93BC 003:093.356 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 003:093.408 - 0.051ms returns 0
T93BC 003:093.463 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 003:093.532   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:094.429 - 0.966ms returns 0x0000006B
T93BC 003:094.486 JLINK_Go()
T93BC 003:094.547   CPU_WriteMem(2 bytes @ 0x20200000)
T93BC 003:095.391   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:099.636 - 5.148ms 
T93BC 003:099.722 JLINK_IsHalted()
T93BC 003:103.342   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:104.100 - 4.377ms returns TRUE
T93BC 003:104.150 JLINK_ReadReg(R15 (PC))
T93BC 003:104.196 - 0.046ms returns 0x20200000
T93BC 003:104.235 JLINK_ClrBPEx(BPHandle = 0x0000006B)
T93BC 003:104.273 - 0.037ms returns 0x00
T93BC 003:104.312 JLINK_ReadReg(R0)
T93BC 003:104.349 - 0.036ms returns 0x00000000
T93BC 003:104.388 JLINK_HasError()
T93BC 003:104.427 JLINK_WriteReg(R0, 0xFFFFFFFF)
T93BC 003:104.464 - 0.037ms returns 0
T93BC 003:104.502 JLINK_WriteReg(R1, 0x00000000)
T93BC 003:104.538 - 0.036ms returns 0
T93BC 003:104.576 JLINK_WriteReg(R2, 0x00009580)
T93BC 003:104.629 - 0.052ms returns 0
T93BC 003:104.667 JLINK_WriteReg(R3, 0x04C11DB7)
T93BC 003:104.703 - 0.036ms returns 0
T93BC 003:104.741 JLINK_WriteReg(R4, 0x00000000)
T93BC 003:104.777 - 0.036ms returns 0
T93BC 003:104.814 JLINK_WriteReg(R5, 0x00000000)
T93BC 003:104.850 - 0.036ms returns 0
T93BC 003:104.888 JLINK_WriteReg(R6, 0x00000000)
T93BC 003:104.924 - 0.035ms returns 0
T93BC 003:104.962 JLINK_WriteReg(R7, 0x00000000)
T93BC 003:104.998 - 0.036ms returns 0
T93BC 003:105.035 JLINK_WriteReg(R8, 0x00000000)
T93BC 003:105.072 - 0.036ms returns 0
T93BC 003:105.109 JLINK_WriteReg(R9, 0x20200290)
T93BC 003:105.145 - 0.036ms returns 0
T93BC 003:105.184 JLINK_WriteReg(R10, 0x00000000)
T93BC 003:105.221 - 0.036ms returns 0
T93BC 003:105.258 JLINK_WriteReg(R11, 0x00000000)
T93BC 003:105.295 - 0.037ms returns 0
T93BC 003:105.333 JLINK_WriteReg(R12, 0x00000000)
T93BC 003:105.369 - 0.036ms returns 0
T93BC 003:105.407 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 003:105.443 - 0.036ms returns 0
T93BC 003:105.481 JLINK_WriteReg(R14, 0x20200001)
T93BC 003:105.517 - 0.036ms returns 0
T93BC 003:105.555 JLINK_WriteReg(R15 (PC), 0x20200002)
T93BC 003:105.591 - 0.036ms returns 0
T93BC 003:105.629 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 003:105.694 - 0.064ms returns 0
T93BC 003:105.732 JLINK_WriteReg(MSP, 0x20208000)
T93BC 003:105.768 - 0.036ms returns 0
T93BC 003:105.805 JLINK_WriteReg(PSP, 0x20208000)
T93BC 003:105.856 - 0.051ms returns 0
T93BC 003:105.894 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 003:105.930 - 0.036ms returns 0
T93BC 003:105.969 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 003:106.006 - 0.038ms returns 0x0000006C
T93BC 003:106.043 JLINK_Go()
T93BC 003:106.091   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:110.329 - 4.285ms 
T93BC 003:110.371 JLINK_IsHalted()
T93BC 003:111.056 - 0.684ms returns FALSE
T93BC 003:111.100 JLINK_HasError()
T93BC 003:116.615 JLINK_IsHalted()
T93BC 003:117.451 - 0.835ms returns FALSE
T93BC 003:117.497 JLINK_HasError()
T93BC 003:118.656 JLINK_IsHalted()
T93BC 003:119.382 - 0.725ms returns FALSE
T93BC 003:119.406 JLINK_HasError()
T93BC 003:120.650 JLINK_IsHalted()
T93BC 003:121.356 - 0.705ms returns FALSE
T93BC 003:121.398 JLINK_HasError()
T93BC 003:122.628 JLINK_IsHalted()
T93BC 003:123.351 - 0.722ms returns FALSE
T93BC 003:123.394 JLINK_HasError()
T93BC 003:124.625 JLINK_IsHalted()
T93BC 003:125.420 - 0.795ms returns FALSE
T93BC 003:125.467 JLINK_HasError()
T93BC 003:126.600 JLINK_IsHalted()
T93BC 003:127.296 - 0.695ms returns FALSE
T93BC 003:127.336 JLINK_HasError()
T93BC 003:130.630 JLINK_IsHalted()
T93BC 003:131.367 - 0.735ms returns FALSE
T93BC 003:131.418 JLINK_HasError()
T93BC 003:132.640 JLINK_IsHalted()
T93BC 003:133.402 - 0.761ms returns FALSE
T93BC 003:133.429 JLINK_HasError()
T93BC 003:134.719 JLINK_IsHalted()
T93BC 003:135.445 - 0.727ms returns FALSE
T93BC 003:135.482 JLINK_HasError()
T93BC 003:136.674 JLINK_IsHalted()
T93BC 003:137.433 - 0.759ms returns FALSE
T93BC 003:137.468 JLINK_HasError()
T93BC 003:138.604 JLINK_IsHalted()
T93BC 003:139.306 - 0.698ms returns FALSE
T93BC 003:139.349 JLINK_HasError()
T93BC 003:140.677 JLINK_IsHalted()
T93BC 003:141.376 - 0.696ms returns FALSE
T93BC 003:141.433 JLINK_HasError()
T93BC 003:142.672 JLINK_IsHalted()
T93BC 003:143.448 - 0.774ms returns FALSE
T93BC 003:143.495 JLINK_HasError()
T93BC 003:144.665 JLINK_IsHalted()
T93BC 003:145.449 - 0.783ms returns FALSE
T93BC 003:145.504 JLINK_HasError()
T93BC 003:147.672 JLINK_IsHalted()
T93BC 003:148.414 - 0.744ms returns FALSE
T93BC 003:148.482 JLINK_HasError()
T93BC 003:149.631 JLINK_IsHalted()
T93BC 003:150.418 - 0.786ms returns FALSE
T93BC 003:150.456 JLINK_HasError()
T93BC 003:151.658 JLINK_IsHalted()
T93BC 003:152.402 - 0.744ms returns FALSE
T93BC 003:152.446 JLINK_HasError()
T93BC 003:153.656 JLINK_IsHalted()
T93BC 003:154.408 - 0.751ms returns FALSE
T93BC 003:154.460 JLINK_HasError()
T93BC 003:155.668 JLINK_IsHalted()
T93BC 003:156.412 - 0.743ms returns FALSE
T93BC 003:156.484 JLINK_HasError()
T93BC 003:158.677 JLINK_IsHalted()
T93BC 003:159.631 - 0.954ms returns FALSE
T93BC 003:159.724 JLINK_HasError()
T93BC 003:161.650 JLINK_IsHalted()
T93BC 003:162.446 - 0.796ms returns FALSE
T93BC 003:162.496 JLINK_HasError()
T93BC 003:163.650 JLINK_IsHalted()
T93BC 003:164.439 - 0.787ms returns FALSE
T93BC 003:164.570 JLINK_HasError()
T93BC 003:166.668 JLINK_IsHalted()
T93BC 003:167.540 - 0.869ms returns FALSE
T93BC 003:167.677 JLINK_HasError()
T93BC 003:169.685 JLINK_IsHalted()
T93BC 003:170.422 - 0.736ms returns FALSE
T93BC 003:170.483 JLINK_HasError()
T93BC 003:171.710 JLINK_IsHalted()
T93BC 003:172.526 - 0.815ms returns FALSE
T93BC 003:172.656 JLINK_HasError()
T93BC 003:174.736 JLINK_IsHalted()
T93BC 003:175.594 - 0.857ms returns FALSE
T93BC 003:175.680 JLINK_HasError()
T93BC 003:177.687 JLINK_IsHalted()
T93BC 003:178.502 - 0.814ms returns FALSE
T93BC 003:178.606 JLINK_HasError()
T93BC 003:180.673 JLINK_IsHalted()
T93BC 003:185.053   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:185.947 - 5.272ms returns TRUE
T93BC 003:186.020 JLINK_ReadReg(R15 (PC))
T93BC 003:186.090 - 0.069ms returns 0x20200000
T93BC 003:186.159 JLINK_ClrBPEx(BPHandle = 0x0000006C)
T93BC 003:186.226 - 0.066ms returns 0x00
T93BC 003:186.294 JLINK_ReadReg(R0)
T93BC 003:186.360 - 0.065ms returns 0x39B281E8
T93BC 003:192.225 JLINK_HasError()
T93BC 003:192.300 JLINK_WriteReg(R0, 0x00000003)
T93BC 003:192.341 - 0.040ms returns 0
T93BC 003:192.378 JLINK_WriteReg(R1, 0x00000000)
T93BC 003:192.415 - 0.036ms returns 0
T93BC 003:192.452 JLINK_WriteReg(R2, 0x00009580)
T93BC 003:192.488 - 0.035ms returns 0
T93BC 003:192.525 JLINK_WriteReg(R3, 0x04C11DB7)
T93BC 003:192.561 - 0.036ms returns 0
T93BC 003:192.598 JLINK_WriteReg(R4, 0x00000000)
T93BC 003:192.634 - 0.035ms returns 0
T93BC 003:192.703 JLINK_WriteReg(R5, 0x00000000)
T93BC 003:192.739 - 0.036ms returns 0
T93BC 003:192.776 JLINK_WriteReg(R6, 0x00000000)
T93BC 003:192.811 - 0.035ms returns 0
T93BC 003:192.849 JLINK_WriteReg(R7, 0x00000000)
T93BC 003:192.884 - 0.035ms returns 0
T93BC 003:192.921 JLINK_WriteReg(R8, 0x00000000)
T93BC 003:192.957 - 0.036ms returns 0
T93BC 003:192.992 JLINK_WriteReg(R9, 0x20200290)
T93BC 003:193.025 - 0.033ms returns 0
T93BC 003:193.060 JLINK_WriteReg(R10, 0x00000000)
T93BC 003:193.093 - 0.033ms returns 0
T93BC 003:193.128 JLINK_WriteReg(R11, 0x00000000)
T93BC 003:193.161 - 0.033ms returns 0
T93BC 003:193.196 JLINK_WriteReg(R12, 0x00000000)
T93BC 003:193.229 - 0.033ms returns 0
T93BC 003:193.267 JLINK_WriteReg(R13 (SP), 0x20208000)
T93BC 003:193.299 - 0.032ms returns 0
T93BC 003:193.332 JLINK_WriteReg(R14, 0x20200001)
T93BC 003:193.373 - 0.041ms returns 0
T93BC 003:193.406 JLINK_WriteReg(R15 (PC), 0x20200094)
T93BC 003:193.438 - 0.031ms returns 0
T93BC 003:193.471 JLINK_WriteReg(XPSR, 0x01000000)
T93BC 003:193.502 - 0.031ms returns 0
T93BC 003:193.536 JLINK_WriteReg(MSP, 0x20208000)
T93BC 003:193.568 - 0.032ms returns 0
T93BC 003:193.601 JLINK_WriteReg(PSP, 0x20208000)
T93BC 003:193.632 - 0.031ms returns 0
T93BC 003:193.674 JLINK_WriteReg(CFBP, 0x00000000)
T93BC 003:193.704 - 0.030ms returns 0
T93BC 003:193.736 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T93BC 003:193.768 - 0.032ms returns 0x0000006D
T93BC 003:193.798 JLINK_Go()
T93BC 003:193.852   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:198.100 - 4.300ms 
T93BC 003:198.216 JLINK_IsHalted()
T93BC 003:201.823   CPU_ReadMem(2 bytes @ 0x20200000)
T93BC 003:202.751 - 4.534ms returns TRUE
T93BC 003:202.848 JLINK_ReadReg(R15 (PC))
T93BC 003:202.893 - 0.045ms returns 0x20200000
T93BC 003:202.934 JLINK_ClrBPEx(BPHandle = 0x0000006D)
T93BC 003:202.973 - 0.039ms returns 0x00
T93BC 003:203.014 JLINK_ReadReg(R0)
T93BC 003:203.053 - 0.039ms returns 0x00000000
T93BC 003:262.222 JLINK_WriteMemEx(0x20200000, 0x00000002 Bytes, Flags = 0x02000000)
T93BC 003:262.314   Data:  FE E7
T93BC 003:262.393   CPU_WriteMem(2 bytes @ 0x20200000)
T93BC 003:263.261 - 1.039ms returns 0x2
T93BC 003:263.317 JLINK_HasError()
T93BC 003:263.363 JLINK_HasError()
T93BC 003:263.399 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T93BC 003:263.434 - 0.034ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T93BC 003:263.468 JLINK_Reset()
T93BC 003:263.515   CPU_ReadMem(4 bytes @ 0x20200000)
T93BC 003:264.219   CPU_WriteMem(4 bytes @ 0x20200000)
T93BC 003:269.412   Memory map 'before startup completion point' is active
T93BC 003:271.828   ResetTarget() start
T93BC 003:271.885    J-Link Script File: Executing ResetTarget()
T93BC 003:271.921   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 003:272.661   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T93BC 003:273.401   CPU_ReadMem(4 bytes @ 0x400B0300)
T93BC 003:274.071   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T93BC 003:274.778   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 003:275.473   CPU_WriteMem(4 bytes @ 0x400B0300)
T93BC 003:276.184   CPU_WriteMem(4 bytes @ 0x400B0304)
T93BC 003:276.925   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T93BC 003:283.540   DAP initialized successfully.
T93BC 003:283.629   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T93BC 003:286.497   ResetTarget() end - Took 12.6ms
T93BC 003:288.076   Device specific reset executed.
T93BC 003:291.546   CPU_WriteMem(4 bytes @ 0x********)
T93BC 003:292.241   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T93BC 003:292.913   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:293.566   CPU_WriteMem(4 bytes @ 0x********)
T93BC 003:294.204 - 30.735ms 
T93BC 003:294.239 JLINK_Go()
T93BC 003:294.270   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:294.999   CPU_WriteMem(4 bytes @ 0x********)
T93BC 003:295.771   CPU_WriteMem(4 bytes @ 0xE0002008)
T93BC 003:295.814   CPU_WriteMem(4 bytes @ 0xE000200C)
T93BC 003:295.853   CPU_WriteMem(4 bytes @ 0xE0002010)
T93BC 003:295.887   CPU_WriteMem(4 bytes @ 0xE0002014)
T93BC 003:297.590   CPU_WriteMem(4 bytes @ 0xE0001004)
T93BC 003:303.664   Memory map 'after startup completion point' is active
T93BC 003:303.757 - 9.516ms 
T93BC 003:315.732 JLINK_Close()
T93BC 003:316.513   CPU is running
T93BC 003:316.611   CPU_WriteMem(4 bytes @ 0xE0002008)
T93BC 003:317.439   CPU is running
T93BC 003:317.486   CPU_WriteMem(4 bytes @ 0xE000200C)
T93BC 003:318.291   CPU is running
T93BC 003:318.340   CPU_WriteMem(4 bytes @ 0xE0002010)
T93BC 003:319.078   CPU is running
T93BC 003:319.122   CPU_WriteMem(4 bytes @ 0xE0002014)
T93BC 003:327.213   OnDisconnectTarget() start
T93BC 003:327.304    J-Link Script File: Executing OnDisconnectTarget()
T93BC 003:331.730   OnDisconnectTarget() end - Took 1.85ms
T93BC 003:331.810   CPU_ReadMem(4 bytes @ 0x********)
T93BC 003:332.539   CPU_WriteMem(4 bytes @ 0x********)
T93BC 003:350.826 - 35.092ms
T93BC 003:350.920   
T93BC 003:350.964   Closed
